import json
from dataclasses import asdict
from datetime import date, datetime, timezone
from typing import Any
from unittest.mock import AsyncMock, patch

import pytest
from starlette.responses import JSONResponse

from marketplace.models import (
    OTACompany,
    OTAPlace,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    ReducedOTAConfig,
    SearchResult,
    Travel,
)
from marketplace.views import bad_request_response
from marketplace.views.search import (
    search_travels_hot,
    search_travels_hot_v3,
)


def _adjust_value(value):
    if isinstance(value, dict):
        return _adjust_datetimes_to_str(value)
    if isinstance(value, datetime):
        return value.isoformat()
    if isinstance(value, date):
        return value.isoformat()
    if isinstance(value, list):
        for lvalue in value:
            lvalue = _adjust_value(lvalue)
    return value


def _adjust_datetimes_to_str(obj: dict[Any, Any]):
    for key, value in obj.items():
        obj[key] = _adjust_value(value)
    return obj


@pytest.fixture
def search_result(ota_config):
    travel = Travel(
        ota="praxio",
        ota_config_id=3,
        code="12345",
        service_code="12345",
        company=ota_config.companies[0],
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 14, 8, 00),
        arrival_at=datetime(2025, 1, 14, 10, 30),
        seat_type=OTASeatType(
            ota_config_id=3,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=-45.00,
        available_seats=0,
        total_seats=0,
        last_synced=datetime(2025, 2, 10, 10, 30),
        extra={
            "IdViagem": 12345,
            "TipoVeiculo": 1,
            "CodigoOrigem": 101,
            "CodigoDestino": 202,
            "Andares": 1,
            "TipoHorario": "Executivo",
        },
    )
    return SearchResult(
        ota_config=ReducedOTAConfig(
            id=ota_config.id, name=ota_config.name, companies=ota_config.companies
        ),
        origin=OTAPlace(
            ota_config_id=2,
            name="Origin City",
            place=Place(id=1, name="Origin City", slug="origin_city"),
            extra={"id": 1001},
        ),
        destination=OTAPlace(
            ota_config_id=2,
            name="Destination City",
            place=Place(id=2, name="Destination City", slug="destination_city"),
            extra={"id": 2001},
        ),
        departure_date=date(2025, 1, 14),
        travels=[travel],
    )


@patch("marketplace.views.search.list_otas", new_callable=AsyncMock)
@patch(
    "marketplace.views.search.places.list_ota_places_by_slugs",
    new_callable=AsyncMock,
)
@patch(
    "marketplace.views.search.get_searcher_from_ota_config",
    return_value=AsyncMock(),
)
async def test_search_travels_hot_v3_with_successful_search(
    mock_search_ota,
    mock_list_ota_places,
    mock_list_otas,
    ota_config,
    search_result,
    create_mock_request,
):
    mock_list_otas.return_value = [ota_config]
    mock_list_ota_places.return_value = {
        ota_config.id: {
            "sao-paulo-sp": ["sao-paulo-sp.terminal-tiete"],
            "rio-de-janeiro-rj": ["rio-de-janeiro-rj"],
        }
    }
    mock_search_ota.return_value.search_multiple.return_value = [search_result]
    # ota with two companies with the same CNPJ and a different one
    ota_config.companies.extend(
        [
            OTACompany(
                name="Test Company Same CNPJ",
                external_id=2,
                cnpj="12345678901234",
                created_at=datetime.now(),
                ota_config_id=4,
            ),
            OTACompany(
                name="Test Company Different CNPJ",
                external_id=3,
                cnpj="43210987654321",
                created_at=datetime.now(),
                ota_config_id=4,
            ),
        ]
    )
    params = {
        "origin": "sao-paulo-sp",
        "destination": "rio-de-janeiro-rj",
        "date": date.today().isoformat(),
    }
    request = create_mock_request(query_params=params)

    response = await search_travels_hot_v3(request)
    assert isinstance(response, JSONResponse)
    expected_item = _adjust_datetimes_to_str(asdict(search_result.travels[0]))
    assert json.loads(bytes(response.body).decode()) == {
        "items": [
            {
                "ota_config": _adjust_datetimes_to_str(asdict(ota_config.reduced())),
                "origin": _adjust_datetimes_to_str(asdict(search_result.origin)),
                "destination": _adjust_datetimes_to_str(
                    asdict(search_result.destination)
                ),
                "departure_date": search_result.departure_date.isoformat(),
                "travels": [expected_item],
                "error": None,
            }
        ]
    }


@pytest.mark.parametrize(
    "query_params",
    [
        {},
        {"origin": "any", "destination": "any", "date": "2025-01-01"},
        {"origin": "any", "destination": "any2", "date": "2025-01-01"},
    ],
    ids=[
        "missing_all_params",
        "same_origin_destination",
        "date_in_past",
    ],
)
async def test_search_travels_invalid_parameters(query_params, create_mock_request):
    request = create_mock_request(query_params=query_params)
    response = await search_travels_hot_v3(request)
    assert response == bad_request_response


@patch("marketplace.views.search.list_otas", new_callable=AsyncMock)
@patch(
    "marketplace.views.search.places.list_ota_places_by_slugs",
    new_callable=AsyncMock,
)
async def test_search_travels_hot_v1_no_origin_destination_match(
    mock_list_ota_places, mock_list_otas, create_mock_request
):
    mock_list_otas.return_value = []
    mock_list_ota_places.return_value = {}

    params = {
        "origin": "sao-paulo-sp",
        "destination": "rio-de-janeiro-rj",
        "date": "2023-12-25",
    }
    request = create_mock_request(query_params=params)

    response = await search_travels_hot(request)
    assert isinstance(response, JSONResponse)
    assert json.loads(bytes(response.body).decode()) == {"items": []}


@patch("marketplace.views.search.list_otas", new_callable=AsyncMock)
@patch(
    "marketplace.views.search.places.list_ota_places_by_slugs",
    new_callable=AsyncMock,
)
@patch(
    "marketplace.views.search.get_searcher_from_ota_config",
    return_value=AsyncMock(),
)
async def test_search_travels_hot_v1_with_successful_search(
    mock_search_ota,
    mock_list_ota_places,
    mock_list_otas,
    ota_config,
    search_result,
    create_mock_request,
):
    mock_list_otas.return_value = [ota_config]
    mock_list_ota_places.return_value = {
        ota_config.id: {
            "sao-paulo-sp": ["sao-paulo-sp.terminal-tiete"],
            "rio-de-janeiro-rj": ["rio-de-janeiro-rj"],
        }
    }
    mock_search_ota.return_value.search_multiple.return_value = [search_result]

    params = {
        "origin": "sao-paulo-sp",
        "destination": "rio-de-janeiro-rj",
        "date": "2023-12-25",
    }
    request = create_mock_request(query_params=params)

    response = await search_travels_hot(request)
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    expected_item = _adjust_datetimes_to_str(asdict(search_result.travels[0]))
    assert response_json == {"items": [expected_item]}
