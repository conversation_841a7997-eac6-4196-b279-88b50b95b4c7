import json
from dataclasses import asdict
from unittest.mock import AsyncMock, patch

from starlette.responses import JSONResponse

from marketplace.models import (
    Seat,
)
from marketplace.views.seats import (
    travel_seating_map,
)


@patch("marketplace.views.seats.get_searcher_from_travel", return_value=AsyncMock())
async def test_travel_seating_map(mock_search_ota, create_mock_request):
    seat = Seat(
        number="2",
        floor=1,
        row=1,
        column=1,
        available=False,
        category="Executivo",
        seat_type="executivo",
        price=150.00,
        extra={"NumeroPoltrona": 1},
    )
    mock_search_ota.return_value.available_seats.return_value = [seat]

    body = {
        "ota_config_id": 2,
        "price": 199.99,
        "extra": {
            "CodigoOrigem": 1001,
            "CodigoDestino": 2001,
            "Andares": 1,
            "IdViagem": "123",
            "TipoServico": 1,
            "TipoVeiculo": 2,
            "TipoHorario": "Executivo",
        },
    }
    request = create_mock_request(json_body=body)

    response = await travel_seating_map(request)
    assert isinstance(response, JSONResponse)
    response_json = json.loads(bytes(response.body).decode())
    expected_item = asdict(seat)
    assert response_json == {"seats": [expected_item]}
