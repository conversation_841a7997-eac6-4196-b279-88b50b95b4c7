from datetime import datetime, timezone
from unittest.mock import AsyncMock, MagicMock

import pytest
from pytest_asyncio import is_async_test
from starlette.requests import Request

from marketplace.models import (
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
)


def pytest_collection_modifyitems(items):
    pytest_asyncio_tests = (item for item in items if is_async_test(item))
    session_scope_marker = pytest.mark.asyncio(loop_scope="session")
    for async_test in pytest_asyncio_tests:
        async_test.add_marker(session_scope_marker, append=False)


@pytest.fixture
def create_mock_request():
    def func(query_params=None, json_body=None):
        mock_request = MagicMock(spec=Request)
        mock_request.query_params = query_params
        mock_request.json = AsyncMock(return_value=json_body)
        mock_request.state.db_pool = MagicMock()
        return mock_request

    return func


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=4,
        name="OTAConfig",
        provider="ota",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=4,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=4,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=now,
                updated_at=now,
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
    )
