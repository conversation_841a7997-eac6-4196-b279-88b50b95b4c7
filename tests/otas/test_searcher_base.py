import asyncio
import time
from datetime import date, datetime, timezone
from unittest import mock
from unittest.mock import AsyncMock, MagicMock

import httpx
import pytest
from yapcache.cache_item import CacheItem

from marketplace.models import (
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    ReducedOTAConfig,
    SearchResult,
)
from marketplace.models import (
    Travel as MarketplaceTravel,
)
from marketplace.otas.exception import OTANotJSONResponse, OTASearcherException
from marketplace.searcher import NotOfferedSection, OTASearcher


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=2,
        name="Eulabs",
        provider="eulabs",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=2,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=2,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            )
        ],
        search_cache=OTASearchCacheConfig(
            ttl=86400, stale_after=900, ttl_few_seats=900
        ),
    )


@pytest.fixture
def searcher(ota_config):
    return OTASearcher(ota_config=ota_config)


async def test_search_multiple(searcher):
    expected_travel = MarketplaceTravel(
        ota="eulabs",
        ota_config_id=2,
        code="1_EXEC",
        service_code="1",
        company=OTACompany(
            name="Test Company",
            external_id=1,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=mock.ANY,
        extra={"class_info_long_name": "Executivo", "key": "travel_key_123"},
    )
    searcher.search = AsyncMock()
    searcher.search.return_value = [expected_travel]

    origin = OTAPlace(
        ota_config_id=2,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )

    origin_2 = OTAPlace(
        ota_config_id=2,
        name="Origin City 2",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=2,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )

    results = await searcher.search_multiple(
        origins=[origin, origin_2],
        destinations=[destination],
        departure_date=date(2025, 1, 9),
        timeout=5,
    )

    assert len(results) == 2

    expected_result = [
        SearchResult(
            ota_config=searcher.ota_config.reduced(),
            origin=origin,
            destination=destination,
            departure_date=date(2025, 1, 9),
            travels=[expected_travel],
        ),
        SearchResult(
            ota_config=ReducedOTAConfig(
                id=searcher.ota_config.id,
                name=searcher.ota_config.name,
                companies=searcher.ota_config.companies,
            ),
            origin=origin_2,
            destination=destination,
            departure_date=date(2025, 1, 9),
            travels=[expected_travel],
        ),
    ]
    assert sorted(results, key=lambda k: k.origin.name) == expected_result


async def test_search_multiple_http_error(searcher):
    searcher.search = AsyncMock()
    searcher.search.side_effect = [OTASearcherException("Erro")]

    origin = OTAPlace(
        ota_config_id=2,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=2,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )

    results = await searcher.search_multiple(
        origins=[origin],
        destinations=[destination],
        departure_date=date(2025, 1, 9),
        timeout=5,
    )

    expected_result = [
        SearchResult(
            ota_config=ReducedOTAConfig(
                id=searcher.ota_config.id,
                name=searcher.ota_config.name,
                companies=searcher.ota_config.companies,
            ),
            origin=origin,
            destination=destination,
            departure_date=date(2025, 1, 9),
            travels=None,
            error="communication_error",
        )
    ]
    assert results == expected_result


async def test_search_multiple_generic_error(searcher):
    searcher.search = AsyncMock()
    searcher.search.side_effect = [Exception("Erro qualquer")]

    origin = OTAPlace(
        ota_config_id=2,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=2,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )

    results = await searcher.search_multiple(
        origins=[origin],
        destinations=[destination],
        departure_date=date(2025, 1, 9),
        timeout=5,
    )

    expected_result = [
        SearchResult(
            ota_config=ReducedOTAConfig(
                id=searcher.ota_config.id,
                name=searcher.ota_config.name,
                companies=searcher.ota_config.companies,
            ),
            origin=origin,
            destination=destination,
            departure_date=date(2025, 1, 9),
            travels=None,
            error="internal_error",
        )
    ]
    assert results == expected_result


async def test_search_multiple_not_json_response(searcher):
    searcher.search = AsyncMock()
    searcher.search.side_effect = [OTANotJSONResponse("Erro qualquer")]

    origin = OTAPlace(
        ota_config_id=2,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=2,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )

    results = await searcher.search_multiple(
        origins=[origin],
        destinations=[destination],
        departure_date=date(2025, 1, 9),
        timeout=5,
    )

    expected_result = [
        SearchResult(
            ota_config=ReducedOTAConfig(
                id=searcher.ota_config.id,
                name=searcher.ota_config.name,
                companies=searcher.ota_config.companies,
            ),
            origin=origin,
            destination=destination,
            departure_date=date(2025, 1, 9),
            travels=None,
            error="communication_error",
        )
    ]
    assert results == expected_result


async def test_search_multiple_timeout(searcher):
    searcher.search = AsyncMock()

    async def delayed_search(*args, **kwargs):
        await asyncio.sleep(1)  # Simula maior tempo de processamento
        return []

    searcher.search.side_effect = delayed_search

    origin = OTAPlace(
        ota_config_id=2,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=2,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )

    results = await searcher.search_multiple(
        origins=[origin],
        destinations=[destination],
        departure_date=date(2025, 1, 9),
        timeout=0,
    )

    expected_result = [
        SearchResult(
            ota_config=ReducedOTAConfig(
                id=searcher.ota_config.id,
                name=searcher.ota_config.name,
                companies=searcher.ota_config.companies,
            ),
            origin=origin,
            destination=destination,
            departure_date=date(2025, 1, 9),
            travels=None,
            error="timeout_error",
        )
    ]
    assert results == expected_result


async def test_dynamic_search_ttl(searcher):
    searcher._has_few_seats_travel = MagicMock(return_value=False)
    with mock.patch("marketplace.searcher.cache.set") as mock_set_cache:
        assert (
            await searcher._dynamic_search_ttl(["travel"], 1, 2, date(2025, 1, 1))
            == searcher.ota_config.search_cache.ttl
        )
    mock_set_cache.assert_awaited_once_with(
        f"empty_responses_counter:{searcher.ota_config.id}:1:2:2025-01-01", 0, ttl=60
    )


async def test_dynamic_search_ttl_empty_result_under_limit(searcher):
    with mock.patch("marketplace.searcher.cache.get", return_value=CacheItem(value=1)):
        assert (
            await searcher._dynamic_search_ttl([], 1, 2, date(2025, 1, 1))
            == searcher.ota_config.search_cache.ttl
        )


async def test_dynamic_search_ttl_empty_result_over_limit(searcher):
    with mock.patch("marketplace.searcher.cache.get", return_value=CacheItem(value=3)):
        assert (
            await searcher._dynamic_search_ttl([], 1, 2, date(2025, 1, 1))
            == searcher.ota_config.search_cache.ttl_not_available
        )


async def test_dynamic_search_ttl_few_seats(searcher):
    searcher._has_few_seats_travel = MagicMock(return_value=True)
    assert (
        await searcher._dynamic_search_ttl(["travel"], 1, 2, date(2025, 1, 1))
        == searcher.ota_config.search_cache.ttl_few_seats
    )


async def test_dynamic_search_ttl_not_offered(searcher):
    error = NotOfferedSection()
    assert (
        searcher._dynamic_search_ttl_on_error(error)
        == searcher.ota_config.search_cache.ttl_not_available
    )


async def test_dynamic_search_ttl_timeout(searcher):
    error = httpx.TimeoutException("error")
    assert 60 <= searcher._dynamic_search_ttl_on_error(error) <= 120


async def test_dynamic_best_before_default(searcher):
    assert searcher._dynamic_best_before(7200) >= time.time()


async def test_dynamic_best_before_high_ttl(searcher):
    assert searcher._dynamic_best_before(
        searcher.ota_config.search_cache.ttl + 10
    ) == float("inf")
