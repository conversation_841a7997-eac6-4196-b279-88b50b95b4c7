from datetime import date, datetime, timezone
from unittest import mock
from unittest.mock import AsyncMock, MagicMock

import httpx
import pytest

from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTASearchCacheConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    PlaceStatus,
    Seat,
    Stopover,
    Travel,
)
from marketplace.otas.exception import OTASearcherTimeoutException
from marketplace.otas.totalbus.client import (
    BloquearPoltronaResponse,
    BuscaOnibusResponse,
    Conexao,
    Empresa,
    ItinerarioResponse,
    Local,
    Localidade,
    LocalidadeParada,
    MapaPoltrona,
    OrigemDestinos,
    OTASectionalNotOffered,
    Parada,
    Preco,
    PricingPoltrona,
    PricingSequencia,
    SearchResponse,
    SeguroOpcional,
    Servico,
    TotalbusClient,
)
from marketplace.otas.totalbus.searcher import TotalbusSearcher


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=4,
        name="Totalbus",
        provider="totalbus",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=4,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=4,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=now,
                updated_at=now,
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
    )


@pytest.fixture
def mock_client():
    return AsyncMock(spec=TotalbusClient)


@pytest.fixture
def searcher(ota_config, mock_client):
    searcher = TotalbusSearcher(
        ota_config=ota_config,
        base_url="https://totalbus.test",
        username="test",
        password="test",
        tenant_id="test",
    )
    searcher._session = mock_client

    return searcher


async def test_search(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=4,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=4,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )
    departure_date = date(2025, 1, 8)
    mock_client.buscar_corrida.return_value = SearchResponse(
        origem=Local(id=1, cidade="Origin City", sigla="OC", uf="UF", empresas=""),
        destino=Local(
            id=1, cidade="Destination city", sigla="DC", uf="UF", empresas=""
        ),
        data="2025-01-08",
        lsServicos=[
            Servico(
                vende=True,
                classe="Executivo",
                empresaId=1,
                empresa="Test Company",
                identificadorViagem="1_EXEC",
                grupoOrigemId=1001,
                grupoDestinoId=2001,
                saida="2025-01-09T06:00:00Z",
                chegada="2025-01-09T12:00:00Z",
                preco=150.00,
                poltronasLivres=38,
                poltronasTotal=42,
                servico="1",
                rutaId=123,
                prefixoLinha="ABC123",
                marcaId=456,
                grupo="Group",
                dataCorrida="2025-01-09",
                dataSaida="2025-01-09T06:00:00Z",
                precoOriginal=200.00,
                mensagemServico="Service message",
                bpe=False,
                km=100.0,
                cnpj="1234567890",
                modalidadeBpe="Modalidade",
                bpeImpressaoPosterior=True,
                numeroOnibus="123",
                tarifa=100.00,
                restricaoDinamicaBloqueioTrecho=True,
                indRutaInternacional=True,
            )
        ],
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 1
    expected_travel = Travel(
        ota="totalbus",
        ota_config_id=4,
        code="1_EXEC",
        service_code="1",
        company=OTACompany(
            name="Test Company",
            external_id=1,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=4,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=4,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=mock.ANY,
        extra={
            "origem": 1001,
            "destino": 2001,
            "dataCorrida": "2025-01-09",
            "servico": 1,
            "classe": "Executivo",
        },
    )
    assert results[0] == expected_travel


async def test_search_stopovers(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=4,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=4,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )
    departure_date = date(2025, 1, 8)
    mock_client.buscar_corrida.return_value = SearchResponse(
        origem=Local(id=1, cidade="CURITIBA - PR", sigla="CBA", uf="PR", empresas="-1"),
        destino=Local(
            id=2, cidade="FLORIANOPOLIS - SC", sigla="FLO", uf="SC", empresas="42"
        ),
        data="2025-01-09",
        lsServicos=[
            Servico(
                identificadorViagem="0000478508533085432605202500000000000945",
                servico="4785",
                rutaId=945,
                prefixoLinha="234",
                marcaId=2,
                grupo="GUERI",
                grupoOrigemId=8533,
                grupoDestinoId=18697,
                saida="2025-05-26 23:30",
                chegada="2025-05-27 19:30",
                dataCorrida="2025-05-26",
                dataSaida="2025-05-26",
                poltronasLivres=12,
                poltronasTotal=12,
                preco=389,
                precoOriginal=359,
                classe="Executivo",
                empresa="GUERINO SEISCENTO",
                empresaId=1,
                mensagemServico="VAI SAO PAULO",
                vende=True,
                bpe=False,
                km=0,
                cnpj="72543978000100",
                modalidadeBpe="",
                bpeImpressaoPosterior=True,
                numeroOnibus="",
                tarifa=155.59,
                restricaoDinamicaBloqueioTrecho=False,
                indRutaInternacional=False,
                conexao=Conexao(
                    servicoConexao="6260",
                    localidadeConexao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    localidadeConexaoId=8543,
                    empresa="GUERINO SEISCENTO",
                    empresaId=1,
                    rutaId=1430,
                    marcaId=2,
                    dataCorridaConexao="2025-05-27",
                    dataSaidaConexao="2025-05-27",
                    primeiroTrechoPoltronasLivres=12,
                    primeiroTrechoPoltronasTotal=12,
                    primeiroTrechoClasse="Executivo",
                    primeiroTrechoDataCorrida="26/05/2025",
                    primeiroTrechoDataSaida="26/05/2025",
                    primeiroTrechoDataChegada="27/05/2025",
                    primeiroTrechoHoraSaida="23:30",
                    primeiroTrechoHoraChegada="05:00",
                    primeiroTrechoPreco="160.00",
                    primeiroTrechoPrecoOriginal="130.00",
                    primeiroTrechoServico="4785",
                    primeiroTrechoLinha=945,
                    primeiroTrechoEmpresa="GUERINO SEISCENTO",
                    primeiroTrechoEmpresaId=1,
                    primeiroTrechoMarca=2,
                    primeiroTrechoOrigem=8533,
                    primeiroTrechoOrigemDescricao="CAMPO GRANDE - MS - TERMINAL RODOVIÁRIO",
                    primeiroTrechoDestino=8543,
                    primeiroTrechoDestinoDescricao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    primeiroTrechoVende=True,
                    primeiroTrechoIsBpe=False,
                    primeiroTrechoSequencia=1,
                    segundoTrechoPoltronasLivres=12,
                    segundoTrechoPoltronasTotal=12,
                    segundoTrechoClasse="Executivo",
                    segundoTrechoDataCorrida="27/05/2025",
                    segundoTrechoDataSaida="27/05/2025",
                    segundoTrechoDataChegada="27/05/2025",
                    segundoTrechoHoraSaida="05:00",
                    segundoTrechoHoraChegada="19:30",
                    segundoTrechoPreco="229.00",
                    segundoTrechoPrecoOriginal="229.00",
                    segundoTrechoServico="6260",
                    segundoTrechoLinha=1430,
                    segundoTrechoEmpresa="GUERINO SEISCENTO",
                    segundoTrechoEmpresaId=1,
                    segundoTrechoMarca=2,
                    segundoTrechoOrigem=8543,
                    segundoTrechoOrigemDescricao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    segundoTrechoDestino=18697,
                    segundoTrechoDestinoDescricao="SAO PAULO - SP - TERMINAL BARRA FUNDA",
                    segundoTrechoVende=True,
                    segundoTrechoIsBpe=False,
                    segundoTrechoSequencia=2,
                    terceiroTrechoVende=False,
                    terceiroTrechoIsBpe=False,
                    vende=True,
                    km=0,
                    cnpj="72543978000100",
                    conexionCtrlId=312,
                    conexionGrupo=6312,
                    bpe=False,
                ),
            )
        ],
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 1
    expected_travel = Travel(
        ota="totalbus",
        ota_config_id=4,
        code="0000478508533085432605202500000000000945",
        service_code="4785",
        company=OTACompany(
            ota_config_id=4,
            external_id=1,
            name="Test Company",
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        itinerary=[],
        origin=Place(
            slug="origin_city",
            name="Origin City",
            id=1,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        departure_at=datetime(2025, 5, 26, 23, 30),
        destination=Place(
            slug="destination_city",
            name="Destination City",
            id=2,
            status=PlaceStatus.ACTIVE,
            created_at=None,
            updated_at=None,
        ),
        arrival_at=datetime(2025, 5, 27, 19, 30),
        seat_type=OTASeatType(
            ota_config_id=4,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc),
        ),
        available_seats=12,
        total_seats=12,
        last_synced=mock.ANY,
        extra={
            "origem": 8533,
            "destino": 18697,
            "dataCorrida": "2025-05-26",
            "servico": 4785,
            "classe": "Executivo",
        },
        single_ticket_connection=False,
        stopovers=[
            Stopover(
                origin_ota_place_id=8533,
                destination_ota_place_id=8543,
                extra={
                    "origem": 8533,
                    "destino": 8543,
                    "dataCorrida": "2025-05-26",
                    "servico": 4785,
                    "classe": "Executivo",
                },
            ),
            Stopover(
                origin_ota_place_id=8543,
                destination_ota_place_id=18697,
                extra={
                    "origem": 8543,
                    "destino": 18697,
                    "dataCorrida": "2025-05-27",
                    "servico": 6260,
                    "classe": "Executivo",
                },
            ),
        ],
        price=389,
    )
    assert results[0] == expected_travel


async def test_search_connections(searcher, mock_client):
    origin = OTAPlace(
        ota_config_id=4,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=4,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )
    departure_date = date(2025, 1, 8)
    mock_client.buscar_corrida.return_value = SearchResponse(
        origem=Local(id=1, cidade="CURITIBA - PR", sigla="CBA", uf="PR", empresas="-1"),
        destino=Local(
            id=2, cidade="FLORIANOPOLIS - SC", sigla="FLO", uf="SC", empresas="42"
        ),
        data="2025-01-09",
        lsServicos=[
            Servico(
                identificadorViagem="0000478508533085432605202500000000000945",
                servico="4785",
                rutaId=945,
                prefixoLinha="234",
                marcaId=2,
                grupo="GUERI",
                grupoOrigemId=8533,
                grupoDestinoId=18697,
                saida="2025-05-26 23:30",
                chegada="2025-05-27 19:30",
                dataCorrida="2025-05-26",
                dataSaida="2025-05-26",
                poltronasLivres=12,
                poltronasTotal=12,
                preco=389,
                precoOriginal=359,
                classe="Executivo",
                empresa="GUERINO SEISCENTO",
                empresaId=1,
                mensagemServico="VAI SAO PAULO",
                vende=True,
                bpe=False,
                km=0,
                cnpj="72543978000100",
                modalidadeBpe="",
                bpeImpressaoPosterior=True,
                numeroOnibus="",
                tarifa=155.59,
                restricaoDinamicaBloqueioTrecho=False,
                indRutaInternacional=False,
                conexao=Conexao(
                    servicoConexao="6260",
                    localidadeConexao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    localidadeConexaoId=8543,
                    empresa="GUERINO SEISCENTO",
                    empresaId=1,
                    rutaId=1430,
                    marcaId=2,
                    dataCorridaConexao="2025-05-27",
                    dataSaidaConexao="2025-05-27",
                    primeiroTrechoPoltronasLivres=12,
                    primeiroTrechoPoltronasTotal=12,
                    primeiroTrechoClasse="Executivo",
                    primeiroTrechoDataCorrida="26/05/2025",
                    primeiroTrechoDataSaida="26/05/2025",
                    primeiroTrechoDataChegada="27/05/2025",
                    primeiroTrechoHoraSaida="23:30",
                    primeiroTrechoHoraChegada="05:00",
                    primeiroTrechoPreco="160.00",
                    primeiroTrechoPrecoOriginal="130.00",
                    primeiroTrechoServico="4785",
                    primeiroTrechoLinha=945,
                    primeiroTrechoEmpresa="GUERINO SEISCENTO",
                    primeiroTrechoEmpresaId=1,
                    primeiroTrechoMarca=2,
                    primeiroTrechoOrigem=8533,
                    primeiroTrechoOrigemDescricao="CAMPO GRANDE - MS - TERMINAL RODOVIÁRIO",
                    primeiroTrechoDestino=8543,
                    primeiroTrechoDestinoDescricao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    primeiroTrechoVende=True,
                    primeiroTrechoIsBpe=False,
                    primeiroTrechoSequencia=1,
                    segundoTrechoPoltronasLivres=12,
                    segundoTrechoPoltronasTotal=12,
                    segundoTrechoClasse="Executivo",
                    segundoTrechoDataCorrida="27/05/2025",
                    segundoTrechoDataSaida="27/05/2025",
                    segundoTrechoDataChegada="27/05/2025",
                    segundoTrechoHoraSaida="06:00",
                    segundoTrechoHoraChegada="19:30",
                    segundoTrechoPreco="229.00",
                    segundoTrechoPrecoOriginal="229.00",
                    segundoTrechoServico="6260",
                    segundoTrechoLinha=1430,
                    segundoTrechoEmpresa="GUERINO SEISCENTO",
                    segundoTrechoEmpresaId=1,
                    segundoTrechoMarca=2,
                    segundoTrechoOrigem=8543,
                    segundoTrechoOrigemDescricao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    segundoTrechoDestino=18697,
                    segundoTrechoDestinoDescricao="SAO PAULO - SP - TERMINAL BARRA FUNDA",
                    segundoTrechoVende=True,
                    segundoTrechoIsBpe=False,
                    segundoTrechoSequencia=2,
                    terceiroTrechoVende=False,
                    terceiroTrechoIsBpe=False,
                    vende=True,
                    km=0,
                    cnpj="72543978000100",
                    conexionCtrlId=312,
                    conexionGrupo=6312,
                    bpe=False,
                ),
            )
        ],
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=departure_date
    )

    assert len(results) == 0


async def test_available_seats(searcher, mock_client):
    mock_client.buscar_onibus_detalhado.return_value = BuscaOnibusResponse(
        origem=Local(
            id=1, cidade="City A", sigla="A", uf="State A", empresas="Company A"
        ),
        destino=Local(
            id=2, cidade="City B", sigla="B", uf="State B", empresas="Company B"
        ),
        data="2022-01-01",
        servico="ABC123",
        dataSaida="2022-01-01 10:00:00",
        dataChegada="2022-01-01 12:00:00",
        mapaPoltrona=[
            MapaPoltrona(
                x="4", y="1", disponivel=True, numero="A1", categoriaReservadaId=1
            ),
            MapaPoltrona(
                x="5", y="2", disponivel=False, numero="A2", categoriaReservadaId=1
            ),
        ],
        lsLocalidadeEmbarque=[
            Localidade(
                id=3, descripcion="Embarque 1", cve="EMB1", indIntegracaoW2i=True
            ),
            Localidade(
                id=4, descripcion="Embarque 2", cve="EMB2", indIntegracaoW2i=True
            ),
        ],
        lsLocalidadeDesembarque=[
            Localidade(
                id=5, descripcion="Desembarque 1", cve="DES1", indIntegracaoW2i=True
            ),
            Localidade(
                id=6, descripcion="Desembarque 2", cve="DES2", indIntegracaoW2i=True
            ),
        ],
        pricingSequencia=[
            PricingSequencia(
                quantidaPoltronas="2",
                precoPoltrona="10.00",
                sequencia="A1,A2",
                tipo="Tipo 1",
            ),
            PricingSequencia(
                quantidaPoltronas="3",
                precoPoltrona="15.00",
                sequencia="A1,A2,A3",
                tipo="Tipo 2",
            ),
        ],
        pricingPoltrona=[
            PricingPoltrona(
                numero="41",
                precoNumero="10.00",
                nome="Poltrona 1",
                pricingId=1,
                porcentagem=50,
            ),
            PricingPoltrona(
                numero="52",
                precoNumero="15.00",
                nome="Poltrona 2",
                pricingId=1,
                porcentagem=50,
            ),
        ],
        poltronasLivres=10,
        empresaCorridaId=123,
        classeServico="Executivo",
        dataCorrida="2022-01-01",
    )

    travel = InputTravel(
        ota_config_id=2,
        extra={
            "origem": 1001,
            "destino": 2001,
            "servico": 1,
            "classe": "Executivo",
            "dataCorrida": "2025-10-20",
        },
    )

    seats = await searcher.available_seats(travel)

    assert len(seats) == 2

    assert seats == [
        Seat(
            number="A1",
            floor=1,
            row=4,
            column=1,
            available=True,
            category="Executivo",
            seat_type="executivo",
            price=None,
            extra={"categoriaReservadaId": 1},
        ),
        Seat(
            number="A2",
            floor=1,
            row=5,
            column=2,
            available=False,
            category="Executivo",
            seat_type="executivo",
            price=None,
            extra={"categoriaReservadaId": 1},
        ),
    ]


async def test_available_seats_invalid_travel(searcher, mock_client):
    mock_client.buscar_onibus_detalhado.return_value = BuscaOnibusResponse(
        origem=Local(
            id=2772,
            cidade="BRASILIA - DF",
            sigla="BSB",
            uf="DF",
            empresas="1",
        ),
        destino=Local(
            id=5410,
            cidade="BELO HORIZONTE - MG",
            sigla="BHZ",
            uf="MG",
            empresas="1",
        ),
        data="2025-02-26",
        servico="2",
        dataSaida="0",
        mapaPoltrona=[],
        lsLocalidadeEmbarque=[],
        lsLocalidadeDesembarque=[],
        pricingSequencia=[],
        pricingPoltrona=[],
    )

    travel = InputTravel(
        ota_config_id=2,
        extra={
            "origem": 1001,
            "destino": 2001,
            "servico": 1,
            "classe": "Executivo",
            "dataCorrida": "2025-10-20",
        },
    )

    seats = await searcher.available_seats(travel)

    assert len(seats) == 0

    assert seats == []


async def test_block_seat(searcher, mock_client):
    mock_client.bloquear_poltrona.return_value = BloquearPoltronaResponse(
        origem=Local(
            id=1, cidade="City A", sigla="A", uf="State A", empresas="Company A"
        ),
        destino=Local(
            id=2, cidade="City B", sigla="B", uf="State B", empresas="Company B"
        ),
        data="2022-01-01",
        servico="Servico",
        assento="A1",
        duracao=120,
        transacao="ABC123",
        preco=Preco(
            tarifa="10.00",
            outros="5.00",
            pedagio="2.00",
            seguro="1.00",
            preco="18.00",
            tarifaComPricing="15.00",
            taxaEmbarque="1.00",
            seguroW2I="0.00",
        ),
        rutaid="RUTA123",
        numOperacion="123456",
        localizador="ABCDEF",
        boletoId=1,
        empresaCorridaId=1,
        dataSaida="2022-01-01 08:00:00",
        dataChegada="2022-01-01 10:00:00",
        dataCorrida="2022-01-01",
        classeServicoId=1,
        seguroOpcional=SeguroOpcional(km=100, valor=10.00),
    )

    travel = Travel(
        ota="totalbus",
        ota_config_id=2,
        code="travel_key_123",
        service_code="1",
        company=OTACompany(
            name="Test Company",
            external_id=2,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=datetime(2024, 1, 1, tzinfo=timezone.utc),
        extra={"origem": 1001, "destino": 2001, "servico": 1, "ruta_id": "123"},
    )

    seat = Seat(
        number="1",
        floor=1,
        row=1,
        column=1,
        available=True,
        category="Executivo",
        seat_type=None,
        price=150.00,
        extra={"categoriaReservadaId": "123"},
    )

    blocked_seat = await searcher.block_seat(travel, seat)

    assert blocked_seat == Seat(
        number="A1",
        available=True,
        category="Executivo",
        seat_type="executivo",
        price=27.00,
        floor=1,
        row=1,
        column=1,
        extra={
            "duracao": 120,
            "tarifa": "10.00",
            "outros": "5.00",
            "pedagio": "2.00",
            "seguro": "1.00",
            "preco": "18.00",
            "tarifaComPricing": "15.00",
            "taxaEmbarque": "1.00",
            "seguroW2I": "0.00",
        },
        block_key="ABC123",
    )


async def test_unblock_seat(searcher, mock_client):
    mock_client.desbloquear_poltrona.return_value = True

    travel = Travel(
        ota="totalbus",
        ota_config_id=2,
        code="travel_key_123",
        service_code="1",
        company=OTACompany(
            name="Test Company",
            external_id=2,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type=None,
            created_at=None,
            updated_at=None,
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=datetime(2024, 1, 1, tzinfo=timezone.utc),
        extra={"origem": 1001, "destino": 2001, "servico": 1, "ruta_id": "123"},
    )

    seat = Seat(
        number="1",
        available=True,
        category="Executivo",
        seat_type="executivo",
        price=150.00,
        floor=1,
        row=1,
        column=1,
        extra={"categoriaReservadaId": "123"},
        block_key="transaction_key_123",
    )

    result = await searcher.unblock_seat(travel, seat)

    assert result is True


async def test_list_places(searcher, mock_client):
    mock_client.consultar_empresas.return_value = [
        Empresa(id=1, nome="Test Company", cnpj="12345678901234")
    ]
    origem = Local(id=1, cidade="City A", sigla="A", uf="State A", empresas="Company A")
    destinos = [
        Local(id=2, cidade="City B", sigla="B", uf="State B", empresas="Company B"),
        Local(id=3, cidade="City C", sigla="C", uf="State C", empresas="Company C"),
        Local(id=4, cidade="City D", sigla="D", uf="State D", empresas="Company D"),
    ]
    mock_client.buscar_origem_destino.return_value = [
        OrigemDestinos(origem=origem, destinos=destinos)
    ]

    places = await searcher.list_places()

    assert len(places) == 4
    ota_places = [
        OTAPlace(ota_config_id=4, name=local.cidade, extra=local)
        for local in [origem] + destinos
    ]
    assert all(ota_place in places for ota_place in ota_places)


async def test_search_travels_timeout(searcher, mock_client):
    mock_client.buscar_corrida.side_effect = httpx.TimeoutException("error")
    searcher.retry_policy = MagicMock(side_effect=lambda x: x)

    with pytest.raises(OTASearcherTimeoutException):
        await searcher._search_travels(date(2025, 1, 1), 1, 2)


async def test_search_travels_not_offered(searcher, mock_client):
    mock_client.buscar_corrida.side_effect = [OTASectionalNotOffered()]
    searcher.retry_policy = MagicMock(side_effect=lambda x: x)
    result, last_synced, cache_ttl = await searcher._search_travels(
        date(2025, 1, 1), 1, 2
    )
    assert result is None
    assert last_synced is not None
    assert cache_ttl == searcher.ota_config.search_cache.ttl_not_available


async def test_travel_itinerary(searcher, mock_client):
    mock_client.busca_itinerario_corrida.return_value = ItinerarioResponse(
        servico="7085",
        data="2025-06-17",
        lsParadas=[
            Parada(
                localidade=LocalidadeParada(
                    id=12722, cidade="CURITIBA - PR -  TERMINAL RODOVIARIO", uf="TE"
                ),
                distancia="112.96",
                permanencia="00:00",
                data="2025-06-17",
                hora="19:00",
                bloqueioCanalVenda=False,
            ),
            Parada(
                localidade=LocalidadeParada(
                    id=13738, cidade="PONTA GROSSA - PR - TERMINAL RODOVIARIO", uf="TE"
                ),
                distancia="33.25",
                permanencia="00:00",
                data="2025-06-17",
                hora="21:00",
                bloqueioCanalVenda=False,
            ),
            Parada(
                localidade=LocalidadeParada(
                    id=12962, cidade="CASTRO - PR - TERMINAL RODOVIARIO", uf="TE"
                ),
                distancia="159.7",
                permanencia="00:00",
                data="2025-06-17",
                hora="21:45",
                bloqueioCanalVenda=False,
            ),
        ],
    )

    travel = InputTravel(
        ota_config_id=2,
        extra={
            "origem": 12722,
            "destino": 13738,
            "servico": 1,
            "classe": "Executivo",
            "dataCorrida": "2025-10-20",
        },
    )
    checkpoints = await searcher.travel_itinerary(travel)

    assert checkpoints == [
        Checkpoint(
            name="CURITIBA - PR -  TERMINAL RODOVIARIO - TE",
            departure_at=datetime(2025, 6, 17, 19, 0),
            distance_km=0,
        ),
        Checkpoint(
            name="PONTA GROSSA - PR - TERMINAL RODOVIARIO - TE",
            departure_at=datetime(2025, 6, 17, 21, 0),
            distance_km=112.96,
        ),
    ]
