from datetime import date

import httpx
import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.totalbus.client import (
    BloquearPoltronaResponse,
    BuscaOnibusResponse,
    Conexao,
    Empresa,
    ItinerarioResponse,
    Local,
    LocalidadeParada,
    MapaPoltrona,
    OrigemDestinos,
    OTASectionalNotOffered,
    Parada,
    Preco,
    SearchResponse,
    Servico,
    TotalbusClient,
)


@pytest.fixture
def client():
    return TotalbusClient(
        base_url="https://totalbus.test",
        username="example",
        password="example",
        tenant_id="test",
    )


async def test_buscar_corrida(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/consultacorrida/buscaCorrida",
        json={
            "origem": {
                "id": 1,
                "cidade": "CURITIBA - PR",
                "sigla": "CBA",
                "uf": "PR",
                "empresas": "-1",
            },
            "destino": {
                "id": 2,
                "cidade": "FLORIANOPOLIS - SC",
                "sigla": "FLO",
                "uf": "SC",
                "empresas": "42",
            },
            "data": "2025-01-09",
            "lsServicos": [
                {
                    "identificadorViagem": "0000678512722174120801202500000000000305",
                    "servico": "6785",
                    "rutaId": 305,
                    "prefixoLinha": "SCSP0123026",
                    "marcaId": 65,
                    "grupo": "NORDE",
                    "grupoOrigemId": 12722,
                    "grupoDestinoId": 17412,
                    "saida": "2025-01-09 06:00",
                    "chegada": "2025-01-09 12:00",
                    "dataCorrida": "2025-01-08",
                    "dataSaida": "2025-01-09",
                    "poltronasLivres": 27,
                    "poltronasTotal": 32,
                    "preco": 161.39,
                    "precoOriginal": 101.27,
                    "classe": "SPACE SEMI-LEITO",
                    "empresa": "NORDESTE TRANSPORTES LTDA",
                    "empresaId": 42,
                    "mensagemServico": "SPACE SEMI-LEITO",
                    "vende": True,
                    "bpe": True,
                    "km": 247,
                    "cnpj": "76299270000107",
                    "modalidadeBpe": "3 - Semileito",
                    "bpeImpressaoPosterior": True,
                    "numeroOnibus": "",
                    "tarifa": 148.78,
                    "restricaoDinamicaBloqueioTrecho": False,
                    "indRutaInternacional": False,
                },
            ],
        },
    )
    search_response = await client.buscar_corrida(
        origem=1, destino=2, data=date(2025, 1, 15)
    )

    assert search_response == SearchResponse(
        origem=Local(
            id=1,
            cidade="CURITIBA - PR",
            sigla="CBA",
            uf="PR",
            empresas="-1",
        ),
        destino=Local(
            id=2,
            cidade="FLORIANOPOLIS - SC",
            sigla="FLO",
            uf="SC",
            empresas="42",
        ),
        data="2025-01-09",
        lsServicos=[
            Servico(
                identificadorViagem="0000678512722174120801202500000000000305",
                servico="6785",
                rutaId=305,
                prefixoLinha="SCSP0123026",
                marcaId=65,
                grupo="NORDE",
                grupoOrigemId=12722,
                grupoDestinoId=17412,
                saida="2025-01-09 06:00",
                chegada="2025-01-09 12:00",
                dataCorrida="2025-01-08",
                dataSaida="2025-01-09",
                poltronasLivres=27,
                poltronasTotal=32,
                preco=161.39,
                precoOriginal=101.27,
                classe="SPACE SEMI-LEITO",
                empresa="NORDESTE TRANSPORTES LTDA",
                empresaId=42,
                mensagemServico="SPACE SEMI-LEITO",
                vende=True,
                bpe=True,
                km=247,
                cnpj="76299270000107",
                modalidadeBpe="3 - Semileito",
                bpeImpressaoPosterior=True,
                numeroOnibus="",
                tarifa=148.78,
                restricaoDinamicaBloqueioTrecho=False,
                indRutaInternacional=False,
                conexao=None,
            ),
        ],
    )


async def test_buscar_corrida_sectional_not_offered(
    httpx_mock: HTTPXMock, client: TotalbusClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/consultacorrida/buscaCorrida",
        status_code=400,
        json={"message": "Trecho não disponível"},
    )

    with pytest.raises(OTASectionalNotOffered):
        await client.buscar_corrida(origem=1, destino=2, data=date(2025, 1, 15))


async def test_buscar_corrida_conexao(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/consultacorrida/buscaCorrida",
        json={
            "origem": {
                "id": 1,
                "cidade": "CURITIBA - PR",
                "sigla": "CBA",
                "uf": "PR",
                "empresas": "-1",
            },
            "destino": {
                "id": 2,
                "cidade": "FLORIANOPOLIS - SC",
                "sigla": "FLO",
                "uf": "SC",
                "empresas": "42",
            },
            "data": "2025-01-09",
            "lsServicos": [
                {
                    "identificadorViagem": "0000478508533085432605202500000000000945",
                    "servico": "4785",
                    "rutaId": 945,
                    "prefixoLinha": "234",
                    "marcaId": 2,
                    "grupo": "GUERI",
                    "grupoOrigemId": 8533,
                    "grupoDestinoId": 18697,
                    "saida": "2025-05-26 23:30",
                    "chegada": "2025-05-27 19:30",
                    "dataCorrida": "2025-05-26",
                    "dataSaida": "2025-05-26",
                    "poltronasLivres": 12,
                    "poltronasTotal": 12,
                    "preco": 389,
                    "precoOriginal": 359,
                    "classe": "CONVENCIONAL",
                    "empresa": "GUERINO SEISCENTO",
                    "empresaId": 1,
                    "mensagemServico": "VAI SAO PAULO",
                    "conexao": {
                        "servicoConexao": "6260",
                        "localidadeConexao": "TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                        "localidadeConexaoId": 8543,
                        "empresa": "GUERINO SEISCENTO",
                        "empresaId": 1,
                        "rutaId": 1430,
                        "marcaId": 2,
                        "dataCorridaConexao": "2025-05-27",
                        "dataSaidaConexao": "2025-05-27",
                        "primeiroTrechoPoltronasLivres": 12,
                        "primeiroTrechoPoltronasTotal": 12,
                        "primeiroTrechoClasse": "CONVENCIONAL",
                        "primeiroTrechoDataCorrida": "26/05/2025",
                        "primeiroTrechoDataSaida": "26/05/2025",
                        "primeiroTrechoDataChegada": "27/05/2025",
                        "primeiroTrechoHoraSaida": "23:30",
                        "primeiroTrechoHoraChegada": "05:00",
                        "primeiroTrechoPreco": "160.00",
                        "primeiroTrechoPrecoOriginal": "130.00",
                        "primeiroTrechoServico": "4785",
                        "primeiroTrechoLinha": 945,
                        "primeiroTrechoEmpresa": "GUERINO SEISCENTO",
                        "primeiroTrechoEmpresaId": 1,
                        "primeiroTrechoMarca": 2,
                        "primeiroTrechoOrigem": 8533,
                        "primeiroTrechoOrigemDescricao": "CAMPO GRANDE - MS - TERMINAL RODOVIÁRIO",
                        "primeiroTrechoDestino": 8543,
                        "primeiroTrechoDestinoDescricao": "TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                        "primeiroTrechoVende": True,
                        "primeiroTrechoIsBpe": False,
                        "primeiroTrechoSequencia": 1,
                        "segundoTrechoPoltronasLivres": 12,
                        "segundoTrechoPoltronasTotal": 12,
                        "segundoTrechoClasse": "LEITO",
                        "segundoTrechoDataCorrida": "27/05/2025",
                        "segundoTrechoDataSaida": "27/05/2025",
                        "segundoTrechoDataChegada": "27/05/2025",
                        "segundoTrechoHoraSaida": "05:00",
                        "segundoTrechoHoraChegada": "19:30",
                        "segundoTrechoPreco": "229.00",
                        "segundoTrechoPrecoOriginal": "229.00",
                        "segundoTrechoServico": "6260",
                        "segundoTrechoLinha": 1430,
                        "segundoTrechoEmpresa": "GUERINO SEISCENTO",
                        "segundoTrechoEmpresaId": 1,
                        "segundoTrechoMarca": 2,
                        "segundoTrechoOrigem": 8543,
                        "segundoTrechoOrigemDescricao": "TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                        "segundoTrechoDestino": 18697,
                        "segundoTrechoDestinoDescricao": "SAO PAULO - SP - TERMINAL BARRA FUNDA",
                        "segundoTrechoVende": True,
                        "segundoTrechoIsBpe": False,
                        "segundoTrechoSequencia": 2,
                        "terceiroTrechoVende": False,
                        "terceiroTrechoIsBpe": False,
                        "vende": True,
                        "km": 0,
                        "cnpj": "72543978000100",
                        "conexionCtrlId": 312,
                        "conexionGrupo": 6312,
                        "bpe": False,
                    },
                    "vende": True,
                    "bpe": False,
                    "km": 0,
                    "cnpj": "72543978000100",
                    "modalidadeBpe": "",
                    "bpeImpressaoPosterior": True,
                    "numeroOnibus": "",
                    "tarifa": 155.59,
                    "restricaoDinamicaBloqueioTrecho": False,
                    "indRutaInternacional": False,
                },
            ],
        },
    )
    search_response = await client.buscar_corrida(
        origem=1, destino=2, data=date(2025, 1, 15)
    )

    assert search_response == SearchResponse(
        origem=Local(id=1, cidade="CURITIBA - PR", sigla="CBA", uf="PR", empresas="-1"),
        destino=Local(
            id=2, cidade="FLORIANOPOLIS - SC", sigla="FLO", uf="SC", empresas="42"
        ),
        data="2025-01-09",
        lsServicos=[
            Servico(
                identificadorViagem="0000478508533085432605202500000000000945",
                servico="4785",
                rutaId=945,
                prefixoLinha="234",
                marcaId=2,
                grupo="GUERI",
                grupoOrigemId=8533,
                grupoDestinoId=18697,
                saida="2025-05-26 23:30",
                chegada="2025-05-27 19:30",
                dataCorrida="2025-05-26",
                dataSaida="2025-05-26",
                poltronasLivres=12,
                poltronasTotal=12,
                preco=389,
                precoOriginal=359,
                classe="CONVENCIONAL",
                empresa="GUERINO SEISCENTO",
                empresaId=1,
                mensagemServico="VAI SAO PAULO",
                vende=True,
                bpe=False,
                km=0,
                cnpj="72543978000100",
                modalidadeBpe="",
                bpeImpressaoPosterior=True,
                numeroOnibus="",
                tarifa=155.59,
                restricaoDinamicaBloqueioTrecho=False,
                indRutaInternacional=False,
                conexao=Conexao(
                    servicoConexao="6260",
                    localidadeConexao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    localidadeConexaoId=8543,
                    empresa="GUERINO SEISCENTO",
                    empresaId=1,
                    rutaId=1430,
                    marcaId=2,
                    dataCorridaConexao="2025-05-27",
                    dataSaidaConexao="2025-05-27",
                    primeiroTrechoPoltronasLivres=12,
                    primeiroTrechoPoltronasTotal=12,
                    primeiroTrechoClasse="CONVENCIONAL",
                    primeiroTrechoDataCorrida="26/05/2025",
                    primeiroTrechoDataSaida="26/05/2025",
                    primeiroTrechoDataChegada="27/05/2025",
                    primeiroTrechoHoraSaida="23:30",
                    primeiroTrechoHoraChegada="05:00",
                    primeiroTrechoPreco="160.00",
                    primeiroTrechoPrecoOriginal="130.00",
                    primeiroTrechoServico="4785",
                    primeiroTrechoLinha=945,
                    primeiroTrechoEmpresa="GUERINO SEISCENTO",
                    primeiroTrechoEmpresaId=1,
                    primeiroTrechoMarca=2,
                    primeiroTrechoOrigem=8533,
                    primeiroTrechoOrigemDescricao="CAMPO GRANDE - MS - TERMINAL RODOVIÁRIO",
                    primeiroTrechoDestino=8543,
                    primeiroTrechoDestinoDescricao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    primeiroTrechoVende=True,
                    primeiroTrechoIsBpe=False,
                    primeiroTrechoSequencia=1,
                    segundoTrechoPoltronasLivres=12,
                    segundoTrechoPoltronasTotal=12,
                    segundoTrechoClasse="LEITO",
                    segundoTrechoDataCorrida="27/05/2025",
                    segundoTrechoDataSaida="27/05/2025",
                    segundoTrechoDataChegada="27/05/2025",
                    segundoTrechoHoraSaida="05:00",
                    segundoTrechoHoraChegada="19:30",
                    segundoTrechoPreco="229.00",
                    segundoTrechoPrecoOriginal="229.00",
                    segundoTrechoServico="6260",
                    segundoTrechoLinha=1430,
                    segundoTrechoEmpresa="GUERINO SEISCENTO",
                    segundoTrechoEmpresaId=1,
                    segundoTrechoMarca=2,
                    segundoTrechoOrigem=8543,
                    segundoTrechoOrigemDescricao="TRES LAGOAS - MS - TERMINAL RODOVIARIO",
                    segundoTrechoDestino=18697,
                    segundoTrechoDestinoDescricao="SAO PAULO - SP - TERMINAL BARRA FUNDA",
                    segundoTrechoVende=True,
                    segundoTrechoIsBpe=False,
                    segundoTrechoSequencia=2,
                    terceiroTrechoVende=False,
                    terceiroTrechoIsBpe=False,
                    vende=True,
                    km=0,
                    cnpj="72543978000100",
                    conexionCtrlId=312,
                    conexionGrupo=6312,
                    bpe=False,
                ),
            )
        ],
    )


async def test_buscar_corrida_raises_http_error_if_status_404(
    httpx_mock: HTTPXMock, client: TotalbusClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/consultacorrida/buscaCorrida",
        status_code=404,
    )
    with pytest.raises(httpx.HTTPStatusError) as exc:
        await client.buscar_corrida(origem=1, destino=2, data=date(2025, 1, 15))
        assert exc.value.response.status_code == 404


async def test_buscar_onibus_detalhado(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/consultaonibus/buscaOnibus",
        json={
            "origem": {
                "id": 12722,
                "cidade": "CURITIBA - PR",
                "sigla": "CBA",
                "uf": "PR",
                "empresas": "-1",
            },
            "destino": {
                "id": 17412,
                "cidade": "FLORIANOPOLIS - SC",
                "sigla": "FLO",
                "uf": "SC",
                "empresas": "42",
            },
            "data": "2025-01-15",
            "servico": "6785",
            "dataSaida": "2025-01-16 06:00:00",
            "dataChegada": "2025-01-16 12:00:00",
            "linha": "",
            "mapaPoltrona": [
                {
                    "x": "1",
                    "y": "0",
                    "disponivel": True,
                    "numero": "01",
                    "categoriaReservadaId": -1,
                },
                {
                    "x": "18",
                    "y": "0",
                    "disponivel": False,
                    "numero": "WC",
                    "categoriaReservadaId": -1,
                },
            ],
            "lsLocalidadeEmbarque": [],
            "lsLocalidadeDesembarque": [],
            "pricingSequencia": [],
            "pricingPoltrona": [],
            "poltronasLivres": 30,
            "empresaCorridaId": 42,
            "classeServico": "SPACE SEMI-LEITO",
            "dataCorrida": "2025-01-15",
            "pricingAplicado": ",208,210",
            "orgaoConcedenteId": 3,
        },
    )

    busca_onibus_response = await client.buscar_onibus_detalhado(
        origem=1, destino=2, data=date(2025, 1, 15), servico=6785
    )

    assert busca_onibus_response == BuscaOnibusResponse(
        origem=Local(
            id=12722,
            cidade="CURITIBA - PR",
            sigla="CBA",
            uf="PR",
            empresas="-1",
        ),
        destino=Local(
            id=17412,
            cidade="FLORIANOPOLIS - SC",
            sigla="FLO",
            uf="SC",
            empresas="42",
        ),
        data="2025-01-15",
        servico="6785",
        dataSaida="2025-01-16 06:00:00",
        dataChegada="2025-01-16 12:00:00",
        mapaPoltrona=[
            MapaPoltrona(
                x="1",
                y="0",
                disponivel=True,
                numero="01",
                categoriaReservadaId=-1,
            ),
            MapaPoltrona(
                x="18",
                y="0",
                disponivel=False,
                numero="WC",
                categoriaReservadaId=-1,
            ),
        ],
        lsLocalidadeEmbarque=[],
        lsLocalidadeDesembarque=[],
        pricingSequencia=[],
        pricingPoltrona=[],
        poltronasLivres=30,
        empresaCorridaId=42,
        classeServico="SPACE SEMI-LEITO",
        dataCorrida="2025-01-15",
    )


async def test_buscar_onibus_nao_encontrado(
    httpx_mock: HTTPXMock, client: TotalbusClient
):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/consultaonibus/buscaOnibus",
        json={
            "origem": {
                "id": 2772,
                "cidade": "BRASILIA - DF",
                "sigla": "BSB",
                "uf": "DF",
                "empresas": "1",
            },
            "destino": {
                "id": 5410,
                "cidade": "BELO HORIZONTE - MG",
                "sigla": "BHZ",
                "uf": "MG",
                "empresas": "1",
            },
            "data": "2025-02-26",
            "servico": "2",
            "dataSaida": "0",
            "linha": "",
            "mapaPoltrona": [],
            "lsLocalidadeEmbarque": [],
            "lsLocalidadeDesembarque": [],
            "pricingSequencia": [],
            "pricingPoltrona": [],
            "pricingAplicado": "0",
        },
    )

    busca_onibus_response = await client.buscar_onibus_detalhado(
        origem=1, destino=2, data=date(2025, 1, 15), servico=6785
    )

    assert busca_onibus_response == BuscaOnibusResponse(
        origem=Local(
            id=2772,
            cidade="BRASILIA - DF",
            sigla="BSB",
            uf="DF",
            empresas="1",
        ),
        destino=Local(
            id=5410,
            cidade="BELO HORIZONTE - MG",
            sigla="BHZ",
            uf="MG",
            empresas="1",
        ),
        data="2025-02-26",
        servico="2",
        dataSaida="0",
        mapaPoltrona=[],
        lsLocalidadeEmbarque=[],
        lsLocalidadeDesembarque=[],
        pricingSequencia=[],
        pricingPoltrona=[],
    )


async def test_bloquear_poltrona(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/bloqueiopoltrona/bloquearPoltrona",
        json={
            "origem": {
                "id": 1,
                "cidade": "CURITIBA - PR",
                "sigla": "CBA",
                "uf": "PR",
                "empresas": "-1",
            },
            "destino": {
                "id": 2,
                "cidade": "FLORIANOPOLIS - SC",
                "sigla": "FLO",
                "uf": "SC",
                "empresas": "42",
            },
            "data": "2025-01-09",
            "servico": "6785",
            "assento": "01",
            "duracao": 360,
            "transacao": "transacao123",
            "preco": {
                "tarifa": "150.00",
                "outros": "0.00",
                "pedagio": "5.00",
                "seguro": "2.00",
                "preco": "157.00",
                "tarifaComPricing": "150.00",
                "taxaEmbarque": "5.00",
                "seguroW2I": "1.50",
            },
            "rutaid": "305",
            "numOperacion": "numOd123",
            "localizador": "loc123",
            "boletoId": 1,
            "empresaCorridaId": 42,
            "dataSaida": "2025-01-09T06:00:00",
            "dataChegada": "2025-01-09T12:00:00",
            "dataCorrida": "2025-01-09",
            "classeServicoId": 1,
        },
    )

    response = await client.bloquear_poltrona(
        origem=1,
        destino=2,
        data=date(2025, 1, 9),
        servico=6785,
        poltrona="01",
    )

    assert response == BloquearPoltronaResponse(
        origem=Local(
            id=1,
            cidade="CURITIBA - PR",
            sigla="CBA",
            uf="PR",
            empresas="-1",
        ),
        destino=Local(
            id=2,
            cidade="FLORIANOPOLIS - SC",
            sigla="FLO",
            uf="SC",
            empresas="42",
        ),
        data="2025-01-09",
        servico="6785",
        assento="01",
        duracao=360,
        transacao="transacao123",
        preco=Preco(
            tarifa="150.00",
            outros="0.00",
            pedagio="5.00",
            seguro="2.00",
            preco="157.00",
            tarifaComPricing="150.00",
            taxaEmbarque="5.00",
            seguroW2I="1.50",
        ),
        rutaid="305",
        seguroOpcional=None,
        numOperacion="numOd123",
        localizador="loc123",
        boletoId=1,
        empresaCorridaId=42,
        dataSaida="2025-01-09T06:00:00",
        dataChegada="2025-01-09T12:00:00",
        dataCorrida="2025-01-09",
        classeServicoId=1,
    )


async def test_desbloquear_poltrona(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/desbloqueiopoltrona/desbloquearPoltrona",
        json={
            "status": True,
        },
    )

    result = await client.desbloquear_poltrona(transacao="ABC123")

    assert result is True


async def test_confirmar_venda(httpx_mock: HTTPXMock, client: TotalbusClient):
    expected_response = {
        "bpe": {
            "linha": "TRES LAGOAS X SAO PAULO",
            "serie": "001",
            "troco": "0",
            "classe": "SEMI-LEITO",
            "outros": "0",
            "qrcode": "https://bpe.fazenda.sp.gov.br/BPe/qrcode?chBPe=35241272543978000100630010036715891596717535&tpAmb=1",
            "seguro": "0",
            "tarifa": "37.31",
            "pedagio": "2.2",
            "prefixo": "MSSP0111009",
            "chaveBpe": "35241272543978000100630010036715891596717535",
            "desconto": "0.00",
            "numeroBpe": "3671589",
            "qrcodeBpe": "https://bpe.fazenda.sp.gov.br/BPe/qrcode?chBPe=35241272543978000100630010036715891596717535&tpAmb=1",
            "plataforma": "21",
            "valorPagar": "50.00",
            "valorTotal": "50.00",
            "tipoServico": "HORARIO ORDINARIO",
            "contingencia": "False",
            "taxaEmbarque": "10.49",
            "tipoDesconto": "Normal",
            "numeroSistema": "219970",
            "outrosTributos": "ICMS:R$ 6,00 (12,00%)  OUTROS TRIB:R$ 2,93 (5,86%) Horário de início do embarque 28/01/2025 09:00. Horário final do embarque: 28/01/2025 09:29  Classe do Serviço: ((C) SEMILEITO)",
            "dataAutorizacao": "2024-12-26T10:22:49-03:00",
            "telefoneEmpresa": "08007700600",
            "cabecalhoAgencia": {
                "uf": "SP",
                "cnpj": "29365880000181",
                "bairro": "CENTRO",
                "cidade": "SAO PAULO",
                "numero": "126",
                "endereco": "RUA DR. GUILHERME BANNITZ",
                "razaoSocial": "BUSER BRASIL TECONOLOGIA LTDA",
            },
            "codigoAnttOrigem": "70",
            "cabecalhoEmitente": {
                "uf": "SP",
                "cep": "17607020",
                "cnpj": "72543978000100",
                "bairro": "VILA ABARCA",
                "cidade": "TUPA",
                "numero": "170",
                "endereco": "R AIMORES",
                "razaoSocial": "GUERINO SEISCENTO",
                "inscricaoEstadual": "697017238117",
            },
            "codigoAnttDestino": "3882",
            "valorPagoNaTarifa": "37.31",
            "codigoMonitriipBPe": "35241272543978000100630010036715891596717535003671589MSSP011120250128093000010000003731000000001234567890000070003882",
            "valorFormaPagamento": "50.00",
            "protocoloAutorizacao": "135240243047155",
        },
        "nome": "João da Silva",
        "preco": "37.31",
        "xmlBPE": '<BPe xmlns="http://www.portalfiscal.inf.br/bpe"><infBPe Id="BPe35241272543978000100630010036715891596717535" versao="1.00"><ide><cUF>35</cUF><tpAmb>1</tpAmb><mod>63</mod><serie>1</serie><nBP>3671589</nBP><cBP>59671753</cBP><cDV>5</cDV><modal>1</modal><dhEmi>2024-12-26T10:22:49-03:00</dhEmi><tpEmis>1</tpEmis><verProc>100</verProc><tpBPe>0</tpBPe><indPres>2</indPres><UFIni>SP</UFIni><cMunIni>3550308</cMunIni><UFFim>SP</UFFim><cMunFim>3509502</cMunFim></ide><emit><CNPJ>72543978000100</CNPJ><IE>697017238117</IE><xNome>GUERINO SEISCENTO</xNome><xFant>GUERINO SEISCENTO</xFant><IM>ISENTO</IM><CNAE>4922101</CNAE><CRT>3</CRT><enderEmit><xLgr>R AIMORES</xLgr><nro>170</nro><xCpl>-</xCpl><xBairro>VILA ABARCA</xBairro><cMun>3555000</cMun><xMun>TUPA</xMun><CEP>17607020</CEP><UF>SP</UF><fone>08007700600</fone></enderEmit><TAR>41</TAR></emit><infPassagem><cLocOrig>3550308</cLocOrig><xLocOrig>SAO PAULO</xLocOrig><cLocDest>3509502</cLocDest><xLocDest>CAMPINAS</xLocDest><dhEmb>2025-01-28T09:30:00-03:00</dhEmb><dhValidade>2025-12-26T10:22:49-03:00</dhValidade><infPassageiro><xNome>João da Silva</xNome><tpDoc>1</tpDoc><nDoc>12345678901</nDoc></infPassageiro></infPassagem><infViagem><cPercurso>1</cPercurso><xPercurso>TRES LAGOAS X SAO PAULO</xPercurso><tpViagem>00</tpViagem><tpServ>3</tpServ><tpAcomodacao>1</tpAcomodacao><tpTrecho>1</tpTrecho><dhViagem>2025-01-28T09:30:00-03:00</dhViagem><prefixo>MSSP0111009</prefixo><poltrona>32</poltrona><plataforma>21</plataforma></infViagem><infValorBPe><vBP>50.00</vBP><vDesconto>0.00</vDesconto><vPgto>50.00</vPgto><vTroco>0.00</vTroco><Comp><tpComp>01</tpComp><vComp>37.31</vComp></Comp><Comp><tpComp>02</tpComp><vComp>2.20</vComp></Comp><Comp><tpComp>03</tpComp><vComp>10.49</vComp></Comp></infValorBPe><imp><ICMS><ICMS00><CST>00</CST><vBC>50.00</vBC><pICMS>12.00</pICMS><vICMS>6.00</vICMS></ICMS00></ICMS></imp><pag><tPag>03</tPag><vPag>50.00</vPag><card><tpIntegra>2</tpIntegra></card></pag><infAdic><infCpl>ICMS:R$ 6,00 (12,00%)  OUTROS TRIB:R$ 2,93 (5,86%) Horário de início do embarque 28/01/2025 09:00. Horário final do embarque: 28/01/2025 09:29  Classe do Serviço: ((C) SEMILEITO)</infCpl></infAdic><infRespTec><CNPJ>00073778000120</CNPJ><xContato>Paulo Rogerio Rocha</xContato><email><EMAIL></email><fone>3121225232</fone></infRespTec></infBPe><infBPeSupl><qrCodBPe>https://bpe.fazenda.sp.gov.br/BPe/qrcode?chBPe=35241272543978000100630010036715891596717535&amp;tpAmb=1</qrCodBPe><boardPassBPe>35241272543978000100630010036715891596717535003671589MSSP011120250128093000010000003731000000001234567890000070003882</boardPassBPe></infBPeSupl><Signature xmlns="http://www.w3.org/2000/09/xmldsig#"><SignedInfo><CanonicalizationMethod Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/><SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/><Reference URI="#BPe35241272543978000100630010036715891596717535"><Transforms><Transform Algorithm="http://www.w3.org/2000/09/xmldsig#enveloped-signature"/><Transform Algorithm="http://www.w3.org/TR/2001/REC-xml-c14n-20010315"/></Transforms><DigestMethod Algorithm="http://www.w3.org/2000/09/xmldsig#sha1"/><DigestValue>KsdbXcbD7bBqWSaCyEPK/itiDC8=</DigestValue></Reference></SignedInfo><SignatureValue>K0SCTc9fRCual/KIgJzBJy8PmlsQ3jIoMuOoNs/WcvxrHVW6x0tR2ls2iY05hZaiuoQ8UfKoGJ6YDKIZWdZglOOZu++jnB5YB3tm2cemChBcjCCFJsGJLOYx+knhadJEDAnJkB/4CuQdyuGUZUQ0i6+Q6yKH6T16n82Q30gcuiIznt2/ILTVKqhLhGOBV9oTXiwkVQ0X1qoKGWgWEn7O9fEvwglZNVPu8n+YdodzVhdtZHBtcyaLNaBgin3ftNBms6DZaSXoC19AmqQi15uKrqZIJFk51avJAq4bIW2a306Y1mwWA/cODWjU4wAMyeIxCZg9tZ1T126MLfjxwljoYQ==</SignatureValue><KeyInfo><X509Data><X509Certificate>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</X509Certificate></X509Data></KeyInfo></Signature></BPe>',
        "agencia": "BUSER",
        "servico": "6485",
        "poltrona": "32",
        "documento": "12345678901",
        "transacao": "eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiIxODY5Ny4xOTMwMS4yMDI1LTAxLTI4LjY0ODUuMTAwMDAwMDc2NjY3NjMuMzIuLTEuZmFsc2UuMzdAMzEiLCJleHAiOjE3NjY3NTcxMjcsInVzZXJJZCI6ImJ1c2VyIiwicm9sZSI6Ii0ifQ.bCIrLKjKCAjcA15yNuBki3o_l8gz1u-VRJ9EMf2NdA4",
        "descOrigem": "SAO PAULO - SP - TERMINAL BARRA FUNDA",
        "certificado": "",
        "descDestino": "CAMPINAS - SP - TERMINAL RODOVIARIO",
        "localizador": "010010089329",
        "numeroBilhete": "10000007666763",
        "numeroSistema": "219970",
        "camposImpressao": {},
        "cupomTaxaEmbarque": "2801202500064850018697001930103235241272543978000100630010036715891596717535",
    }
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/confirmavenda/confirmarVenda",
        json=expected_response,
    )

    response = await client.confirmar_venda(
        transacao="ABC123",
        nome_passageiro="João da Silva",
        documento_passageiro="12345678901",
        telefone="1234567890",
    )

    # TODO: type this response (create ConfirmarVendaResponse dataclass)
    assert response == expected_response


async def test_cancelar_venda(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/cancelavenda/cancelarVenda",
        json={"status": "success"},
    )

    response = await client.cancelar_venda(transacao="ABC123", validar_multa=False)

    assert response == {"status": "success"}


async def test_consultar_empresas(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="GET",
        url="https://totalbus.test/api-gateway/catalogo/consultarEmpresas",
        json=[
            {"id": 42, "nome": "NORDESTE TRANSPORTES LTDA", "cnpj": "76299270000107"},
            {"id": 43, "nome": "SUL TRANSPORTES LTDA", "cnpj": "76299270000108"},
        ],
    )

    empresas = await client.consultar_empresas()

    assert empresas == [
        Empresa(id=42, nome="NORDESTE TRANSPORTES LTDA", cnpj="76299270000107"),
        Empresa(id=43, nome="SUL TRANSPORTES LTDA", cnpj="76299270000108"),
    ]


async def test_buscar_origem_destino(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="GET",
        url="https://totalbus.test/api-gateway/localidade/buscarOrigenDestino/42",
        json=[
            {
                "origem": {
                    "id": 1,
                    "cidade": "CURITIBA - PR",
                    "sigla": "CBA",
                    "uf": "PR",
                    "empresas": "-1",
                },
                "destinos": [
                    {
                        "id": 2,
                        "cidade": "FLORIANOPOLIS - SC",
                        "sigla": "FLO",
                        "uf": "SC",
                        "empresas": "42",
                    },
                    {
                        "id": 3,
                        "cidade": "JOINVILLE - SC",
                        "sigla": "JOI",
                        "uf": "SC",
                        "empresas": "42",
                    },
                ],
            }
        ],
    )

    origem_destinos = await client.buscar_origem_destino(42)

    assert origem_destinos == [
        OrigemDestinos(
            origem=Local(
                id=1, cidade="CURITIBA - PR", sigla="CBA", uf="PR", empresas="-1"
            ),
            destinos=[
                Local(
                    id=2,
                    cidade="FLORIANOPOLIS - SC",
                    sigla="FLO",
                    uf="SC",
                    empresas="42",
                ),
                Local(
                    id=3, cidade="JOINVILLE - SC", sigla="JOI", uf="SC", empresas="42"
                ),
            ],
        )
    ]


async def test_busca_itinerario_corrida(httpx_mock: HTTPXMock, client: TotalbusClient):
    httpx_mock.add_response(
        method="POST",
        url="https://totalbus.test/api-gateway/itinerario/buscarItinerarioCorrida",
        json={
            "servico": "7085",
            "data": "2025-06-17",
            "lsParadas": [
                {
                    "localidade": {
                        "id": 12722,
                        "cidade": "CURITIBA - PR -  TERMINAL RODOVIARIO",
                        "uf": "TE",
                    },
                    "distancia": "112.96",
                    "permanencia": "00:00",
                    "data": "2025-06-17",
                    "hora": "19:00",
                    "bloqueioCanalVenda": False,
                },
                {
                    "localidade": {
                        "id": 13738,
                        "cidade": "PONTA GROSSA - PR - TERMINAL RODOVIARIO",
                        "uf": "TE",
                    },
                    "distancia": "33.25",
                    "permanencia": "00:00",
                    "data": "2025-06-17",
                    "hora": "21:00",
                    "bloqueioCanalVenda": False,
                },
                {
                    "localidade": {
                        "id": 12962,
                        "cidade": "CASTRO - PR - TERMINAL RODOVIARIO",
                        "uf": "TE",
                    },
                    "distancia": "159.7",
                    "permanencia": "00:00",
                    "data": "2025-06-17",
                    "hora": "21:45",
                    "bloqueioCanalVenda": False,
                },
            ],
        },
    )

    itinerario = await client.busca_itinerario_corrida(72, "2025-06-02")

    assert itinerario == ItinerarioResponse(
        servico="7085",
        data="2025-06-17",
        lsParadas=[
            Parada(
                localidade=LocalidadeParada(
                    id=12722, cidade="CURITIBA - PR -  TERMINAL RODOVIARIO", uf="TE"
                ),
                distancia="112.96",
                permanencia="00:00",
                data="2025-06-17",
                hora="19:00",
                bloqueioCanalVenda=False,
            ),
            Parada(
                localidade=LocalidadeParada(
                    id=13738, cidade="PONTA GROSSA - PR - TERMINAL RODOVIARIO", uf="TE"
                ),
                distancia="33.25",
                permanencia="00:00",
                data="2025-06-17",
                hora="21:00",
                bloqueioCanalVenda=False,
            ),
            Parada(
                localidade=LocalidadeParada(
                    id=12962, cidade="CASTRO - PR - TERMINAL RODOVIARIO", uf="TE"
                ),
                distancia="159.7",
                permanencia="00:00",
                data="2025-06-17",
                hora="21:45",
                bloqueioCanalVenda=False,
            ),
        ],
    )
