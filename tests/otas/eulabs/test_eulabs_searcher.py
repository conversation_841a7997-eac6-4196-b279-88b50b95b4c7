from datetime import date, datetime, timezone
from unittest import mock
from unittest.mock import AsyncMock, MagicMock

import httpx
import pytest
from pytest_httpx import HTTPXMock

from marketplace.circuit_breaker import CircuitState
from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTASearchCacheConfig,
    OTASearchCircuitBreakerConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    Seat,
)
from marketplace.models import (
    Travel as MarketplaceTravel,
)
from marketplace.otas.eulabs.client import (
    BenefitValue,
    BlockSeatResponse,
    ClassCategory,
    ClassInfo,
    Company,
    EulabsClient,
    FreeSeatBenefit,
    Item,
    SeatingMap,
    Sectional,
    Tariff,
    Travel,
    TravelLeg,
)
from marketplace.otas.eulabs.client import (
    Seat as EulabsSeat,
)
from marketplace.otas.eulabs.searcher import EulabsSearcher
from marketplace.otas.exception import OTASearcherException, OTASearcherTimeoutException


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=2,
        name="Eulabs",
        provider="eulabs",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Test Company",
                external_id=1,
                cnpj="12345678901234",
                created_at=now,
                ota_config_id=2,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=2,
                name="Executivo",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="executivo",
                created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
                updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
    )


@pytest.fixture
def mock_client():
    return AsyncMock(spec=EulabsClient)


@pytest.fixture
def searcher(ota_config, mock_client):
    searcher = EulabsSearcher(
        ota_config=ota_config,
        base_url="https://eulabs.test",
        api_id="test",
        api_key="test",
    )
    searcher._session = mock_client

    return searcher


async def test_search(searcher, mock_client):
    mock_client.travels_search.return_value = [
        Travel(
            key="travel_key_123",
            id=1,
            origin_sectional_id=1001,
            destiny_sectional_id=2001,
            datetime_departure="2025-01-09T06:00:00Z",
            datetime_arrival="2025-01-09T12:00:00Z",
            duration="6:00",
            price=150.00,
            price_promotional=120.00,
            free_seats=38,
            free_seats_benefits=[FreeSeatBenefit(type="student", free=2, amount=38)],
            items=[
                Item(
                    id=1,
                    road_item_id=101,
                    gateway_id=1,
                    gateway_type="road",
                    station_id_origin=1001,
                    station_id_destiny=2001,
                    service_travel="EXEC",
                    reference_travel="REF123",
                    datetime_departure="2025-01-09T06:00:00Z",
                    datetime_arrival="2025-01-09T12:00:00Z",
                    duration="6:00",
                    free_seats=38,
                    tariff=120.00,
                    insurance=5.00,
                    fee=10.00,
                    travel_item_toll=15.00,
                    price=150.00,
                    price_promotional=120.00,
                    tax=0.00,
                    class_info=ClassInfo(
                        id=1, long_name="Executivo", short_name="EXEC"
                    ),
                    company=Company(
                        id=1,
                        code="TEST",
                        name="Test Company",
                        logo="",
                    ),
                    line_code="LINE123",
                    direction="IDA",
                    vehicle_id=42,
                    tariffs=[
                        Tariff(
                            tariff=120.00,
                            insurance=5.00,
                            toll=15.00,
                            boarding_fee=10.00,
                            ferry=0.00,
                            additional=0.00,
                            calculation=150.00,
                            referential=150.00,
                            resource_discount=0.00,
                            total_km=500.00,
                            price_promotional=120.00,
                            amount=150.00,
                            category=ClassCategory(
                                seat_map_id=1,
                                vehicle_type_id=1,
                                description="Executivo",
                                short_description="EXEC",
                                category_sat="02",
                                initial_seat=1,
                                final_seat=42,
                                free_seats=38,
                            ),
                        )
                    ],
                )
            ],
        )
    ]

    origin = OTAPlace(
        ota_config_id=2,
        name="Origin City",
        place=Place(id=1, name="Origin City", slug="origin_city"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=2,
        name="Destination City",
        place=Place(id=2, name="Destination City", slug="destination_city"),
        extra={"id": 2001},
    )

    results = await searcher.search(
        origin=origin, destination=destination, departure_date=date(2025, 1, 9)
    )

    assert len(results) == 1
    expected_travel = MarketplaceTravel(
        ota="eulabs",
        ota_config_id=2,
        code="1_EXEC",
        service_code="1",
        company=OTACompany(
            name="Test Company",
            external_id=1,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=mock.ANY,
        extra={
            "category_description": "Executivo",
            "class_info_long_name": "Executivo",
            "key": "travel_key_123",
            "id": 1,
            "origin_id": 1001,
            "destination_id": 2001,
        },
    )
    assert results[0] == expected_travel


async def test_get_price(searcher, mock_client):
    tariff = Tariff(
        tariff=120.00,
        insurance=5.00,
        toll=15.00,
        boarding_fee=10.00,
        ferry=0.00,
        additional=0.00,
        calculation=150.00,
        referential=150.00,
        resource_discount=0.00,
        total_km=500.00,
        price_promotional=120.00,
        amount=150.00,
        category=ClassCategory(
            seat_map_id=1,
            vehicle_type_id=1,
            description="Executivo",
            short_description="EXEC",
            category_sat="02",
            initial_seat=1,
            final_seat=42,
            free_seats=38,
        ),
    )
    assert searcher._get_price(tariff) == 150.00
    searcher.extra_config.use_promotional_price = True
    assert searcher._get_price(tariff) == 120.00


async def test_list_places(searcher, mock_client):
    mock_client.sectionals.return_value = [
        Sectional(code="TEST", description="Test City", id=1001, uf_acronym="SP")
    ]

    places = await searcher.list_places()

    assert places == [
        OTAPlace(
            ota_config_id=2,
            name="Test City SP",
            extra=Sectional(
                code="TEST", description="Test City", id=1001, uf_acronym="SP"
            ),
        )
    ]


async def test_available_seats(searcher, mock_client):
    mock_client.seating_map.return_value = [
        SeatingMap(
            floor_1=[
                EulabsSeat(
                    number=1,
                    line=1,
                    column=1,
                    busy=False,
                    category="EXEC",
                    tariff=120.00,
                    amount=150.00,
                    aditional_amount=0.00,
                    price_discount=0.00,
                    woman_space=False,
                    benefits_values=[
                        BenefitValue(
                            type="student",
                            tarrif=60.00,
                            price_discount=60.00,
                            amount=90.00,
                        )
                    ],
                )
            ],
            floor_2=None,
            free_seats=41,
        )
    ]

    travel = InputTravel(
        ota_config_id=2,
        extra={"key": "travel_key_123"},
    )

    seats = await searcher.available_seats(travel)

    assert seats == [
        Seat(
            number="1",
            floor=1,
            row=1,
            column=1,
            available=True,
            category="EXEC",
            seat_type=None,
            price=150.00,
            extra={
                "tariff": 120.00,
                "aditional_amount": 0.00,
                "price_discount": 0.00,
                "woman_space": False,
                "beneffits_values": [
                    {
                        "type": "student",
                        "tarrif": 60.00,
                        "price_discount": 60.00,
                        "amount": 90.00,
                    }
                ],
            },
        )
    ]


async def test_block_seat(searcher, mock_client):
    mock_client.block_seats.return_value = BlockSeatResponse(
        selected_seat_key="seat_key_123"
    )

    travel = MarketplaceTravel(
        ota="eulabs",
        ota_config_id=2,
        code="1_EXEC",
        service_code="1",
        company=OTACompany(
            name="Test Company",
            external_id=123,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=datetime(2024, 1, 1, tzinfo=timezone.utc),
        extra={"key": "travel_key_123"},
    )

    seat = Seat(
        number="1",
        floor=1,
        row=1,
        column=1,
        available=True,
        category="EXEC",
        seat_type=None,
        price=150.00,
        extra={
            "tariff": 120.00,
            "aditional_amount": 0.00,
            "price_discount": 0.00,
            "woman_space": False,
        },
    )

    blocked_seat = await searcher.block_seat(travel, seat)

    assert blocked_seat == Seat(
        number="1",
        floor=1,
        row=1,
        column=1,
        available=True,
        category="EXEC",
        seat_type=None,
        price=150.00,
        extra={
            "tariff": 120.00,
            "aditional_amount": 0.00,
            "price_discount": 0.00,
            "woman_space": False,
        },
        block_key="seat_key_123",
    )


async def test_unblock_seat(searcher, mock_client):
    travel = MarketplaceTravel(
        ota="eulabs",
        ota_config_id=2,
        code="1_EXEC",
        service_code="1",
        company=OTACompany(
            name="Test Company",
            external_id=123,
            cnpj="12345678901234",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            ota_config_id=2,
        ),
        itinerary=[],
        origin=Place(id=1, name="Origin City", slug="origin_city"),
        destination=Place(id=2, name="Destination City", slug="destination_city"),
        departure_at=datetime(2025, 1, 9, 6, 0, tzinfo=timezone.utc),
        arrival_at=datetime(2025, 1, 9, 12, 0, tzinfo=timezone.utc),
        seat_type=OTASeatType(
            ota_config_id=2,
            name="Executivo",
            status=OTASeatTypeStatus.AVAILABLE,
            seat_type="executivo",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=150.00,
        available_seats=38,
        total_seats=42,
        last_synced=datetime(2024, 1, 1, tzinfo=timezone.utc),
        extra={"key": "travel_key_123"},
    )

    seat = Seat(
        number="1",
        floor=1,
        row=1,
        column=1,
        available=True,
        category="EXEC",
        seat_type=None,
        price=150.00,
        extra={
            "tariff": 120.00,
            "aditional_amount": 0.00,
            "price_discount": 0.00,
            "woman_space": False,
        },
        block_key="seat_key_123",
    )

    result = await searcher.unblock_seat(travel, seat)
    assert result is True


async def test_search_travels_timeout(searcher, mock_client):
    mock_client.travels_search.side_effect = httpx.TimeoutException("error")
    searcher.retry_policy = MagicMock(side_effect=lambda x: x)
    with pytest.raises(OTASearcherTimeoutException):
        await searcher._search_travels(date(2025, 1, 1), 1, 2)


async def test_search_travels_open_circuit_after_errors(httpx_mock: HTTPXMock):
    searcher = EulabsSearcher(
        ota_config=OTAConfig(
            id=1,
            name="Example",
            provider="example",
            status=OTAConfigStatus.active,
            created_at=mock.ANY,
            updated_at=mock.ANY,
            companies=mock.ANY,
            seat_types=mock.ANY,
            search_cache=mock.ANY,
            circuit_breaker=OTASearchCircuitBreakerConfig(
                enabled=True,
                failure_threshold=2,
            ),
        ),
        base_url="http://example.com",
        api_id="test",
        api_key="test",
    )
    searcher.retry_policy = lambda v: v

    origin = OTAPlace(ota_config_id=1, name="place-1", extra={"id": 1})
    destination = OTAPlace(ota_config_id=1, name="place-2", extra={"id": 2})

    httpx_mock.add_exception(
        httpx.ReadTimeout("Unable to read within timeout"),
        is_reusable=True,
    )
    for _ in range(3):
        try:
            await searcher.search(origin, destination, date(2025, 1, 15))
        except OTASearcherException:
            ...

    assert searcher._circuit_breaker.state == CircuitState.OPEN


async def test_travel_itinerary(searcher, mock_client):
    mock_client.get_road_travel_summary.return_value = [
        TravelLeg(
            arrival_zone="SP",
            departure_time_zone="SP",
            local_arrival_date_time="2025-01-09T12:00:00",
            local_exit="2025-01-09T06:00:00",
            seccional_code="TEST",
            seccional_id=1,
            seccional_name="Test Station 1",
            stop_time="00:00",
            total_time="00:00",
            total_km=0.0,
            uf_acronym="SP",
        ),
        TravelLeg(
            arrival_zone="SP",
            departure_time_zone="SP",
            local_arrival_date_time="2025-01-09T12:00:00",
            local_exit="2025-01-09T12:00:00",
            seccional_code="TEST",
            seccional_id=2,
            seccional_name="Test Station 2",
            stop_time="00:00",
            total_time="06:00",
            total_km=300.0,
            uf_acronym="SP",
        ),
        TravelLeg(
            arrival_zone="SP",
            departure_time_zone="SP",
            local_arrival_date_time="2025-01-09T12:00:00",
            local_exit="2025-01-09T13:00:00",
            seccional_code="TEST",
            seccional_id=3,
            seccional_name="Test Station 3",
            stop_time="00:00",
            total_time="07:00",
            total_km=350.0,
            uf_acronym="SP",
        ),
    ]

    travel = InputTravel(
        ota_config_id=2,
        extra={
            "id": 123,
            "origin_id": 2,
            "destination_id": 3,
            "class_info_long_name": "Executivo",
            "key": "key",
        },
    )
    checkpoints = await searcher.travel_itinerary(travel)

    assert checkpoints == [
        Checkpoint(
            name="Test Station 2",
            departure_at=datetime(2025, 1, 9, 12, 0),
            distance_km=0,
        ),
        Checkpoint(
            name="Test Station 3",
            departure_at=datetime(2025, 1, 9, 13, 0),
            distance_km=50,
        ),
    ]
