from datetime import date

import httpx
import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.eulabs.client import (
    BlockSeatRequest,
    ClassInfo,
    Company,
    EulabsClient,
    FreeSeatBenefit,
    Item,
    Seat,
    SeatingMap,
    Sectional,
    Travel,
    TravelDetail,
    TravelLeg,
)


@pytest.fixture
def client():
    return EulabsClient(
        base_url="https://eulabs.test",
        api_id="test",
        api_key="test",
    )


async def test_travels_search(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://eulabs.test/road/travels/search"
            "?departure_date=2025-01-09"
            "&origin_sectional_id=1001"
            "&destiny_sectional_id=2001"
        ),
        json=[
            {
                "key": "travel_key_123",
                "id": 1,
                "origin_sectional_id": 1001,
                "destiny_sectional_id": 2001,
                "datetime_departure": "2025-01-09T06:00:00Z",
                "datetime_arrival": "2025-01-09T12:00:00Z",
                "duration": "6:00",
                "price": 150.00,
                "price_promotional": 120.00,
                "free_seats": 38,
                "free_seats_benefits": [{"type": "student", "free": 2, "amount": 38}],
                "items": [
                    {
                        "id": 1,
                        "road_item_id": 101,
                        "gateway_id": 1,
                        "gateway_type": "road",
                        "station_id_origin": 1001,
                        "station_id_destiny": 2001,
                        "service_travel": "EXEC",
                        "reference_travel": "REF123",
                        "datetime_departure": "2025-01-09T06:00:00Z",
                        "datetime_arrival": "2025-01-09T12:00:00Z",
                        "duration": "6:00",
                        "free_seats": 38,
                        "tariff": 120.00,
                        "insurance": 5.00,
                        "fee": 10.00,
                        "travel_item_toll": 15.00,
                        "price": 150.00,
                        "price_promotional": 120.00,
                        "tax": 0.00,
                        "line_code": "LINE123",
                        "direction": "IDA",
                        "vehicle_id": 42,
                        "class": {
                            "id": 1,
                            "long_name": "Executivo",
                            "short_name": "EXEC",
                        },
                        "company": {
                            "id": 1,
                            "name": "Test Company",
                            "code": "TEST",
                            "logo": "",
                        },
                        "tariffs": None,
                    }
                ],
            }
        ],
    )

    response = await client.travels_search(
        departure_date=date(2025, 1, 9),
        origin_sectional_id=1001,
        destiny_sectional_id=2001,
    )

    assert response == [
        Travel(
            key="travel_key_123",
            id=1,
            origin_sectional_id=1001,
            destiny_sectional_id=2001,
            datetime_departure="2025-01-09T06:00:00Z",
            datetime_arrival="2025-01-09T12:00:00Z",
            duration="6:00",
            price=150.00,
            price_promotional=120.00,
            free_seats=38,
            free_seats_benefits=[FreeSeatBenefit(type="student", free=2, amount=38)],
            items=[
                Item(
                    id=1,
                    road_item_id=101,
                    gateway_id=1,
                    gateway_type="road",
                    station_id_origin=1001,
                    station_id_destiny=2001,
                    service_travel="EXEC",
                    reference_travel="REF123",
                    datetime_departure="2025-01-09T06:00:00Z",
                    datetime_arrival="2025-01-09T12:00:00Z",
                    duration="6:00",
                    free_seats=38,
                    tariff=120.00,
                    insurance=5.00,
                    fee=10.00,
                    travel_item_toll=15.00,
                    price=150.00,
                    price_promotional=120.00,
                    tax=0.00,
                    line_code="LINE123",
                    direction="IDA",
                    vehicle_id=42,
                    class_info=ClassInfo(
                        id=1, long_name="Executivo", short_name="EXEC"
                    ),
                    company=Company(id=1, code="TEST", name="Test Company", logo=""),
                    tariffs=None,
                )
            ],
        )
    ]


async def test_travels_search_raises_http_error_if_status_404(
    httpx_mock: HTTPXMock, client: EulabsClient
):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://eulabs.test/road/travels/search"
            "?departure_date=2025-01-09"
            "&origin_sectional_id=1001"
            "&destiny_sectional_id=2001"
        ),
        status_code=404,
    )
    with pytest.raises(httpx.HTTPStatusError) as exc:
        await client.travels_search(
            departure_date=date(2025, 1, 9),
            origin_sectional_id=1001,
            destiny_sectional_id=2001,
        )
        assert exc.value.response.status_code == 404


async def test_list_road_travels_summary(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/summary?initial_departure_date=2025-01-09&final_departure_date=2025-01-10",
        json=[
            {
                "id": 1,
                "line_code": "LINE123",
                "departure_date": "2025-01-09",
                "departure_time": "06:00",
                "description": "Test Travel",
                "company_id": 1,
                "schedule_id": 101,
            }
        ],
    )

    summaries = await client.list_road_travels_summary(
        initial_departure_date=date(2025, 1, 9),
        final_departure_date=date(2025, 1, 10),
    )

    assert len(summaries) == 1
    summary = summaries[0]
    assert summary.line_code == "LINE123"
    assert summary.departure_date == "2025-01-09"


async def test_seating_map(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/travel_key_123/seating-map",
        json=[
            {
                "floor_1": [
                    {
                        "number": 1,
                        "line": 1,
                        "column": 1,
                        "busy": False,
                        "category": "EXEC",
                        "tariff": 120.00,
                        "amount": 150.00,
                        "aditional_amount": 0.00,
                        "price_discount": 0.00,
                        "woman_space": False,
                        "benefits_values": [],
                    }
                ],
                "floor_2": None,
                "free_seats": 41,
            }
        ],
    )

    seating_maps = await client.seating_map("travel_key_123")

    assert seating_maps == [
        SeatingMap(
            floor_1=[
                Seat(
                    number=1,
                    line=1,
                    column=1,
                    busy=False,
                    category="EXEC",
                    tariff=120.00,
                    amount=150.00,
                    aditional_amount=0.00,
                    price_discount=0.00,
                    woman_space=False,
                    benefits_values=[],
                )
            ],
            floor_2=None,
            free_seats=41,
        )
    ]


async def test_seating_map_invalid_travel(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/travel_key_123/seating-map",
        json={"message": "Viagem informada é inválida!"},
        status_code=400,
    )

    with pytest.raises(httpx.HTTPStatusError):
        await client.seating_map("travel_key_123")


async def test_block_seats(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="POST",
        url="https://eulabs.test/road/travels/travel_key_123/seats",
        json={"selected_seat_Key": "seat_key_123"},
    )

    block_seats_request = [BlockSeatRequest(seat=1)]
    response = await client.block_seats("travel_key_123", block_seats_request)

    assert response.selected_seat_key == "seat_key_123"


async def test_unblock_seats(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="DELETE",
        url="https://eulabs.test/road/travels/travel_key_123/selected_seats/seat_key_123",
        status_code=204,
    )

    await client.unblock_seats("travel_key_123", "seat_key_123")


async def test_sectionals(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/sectionals?id=1&code=TEST&is_road_station=false&locality_id=100",
        json=[
            {"code": "TEST", "description": "Test Station", "id": 1, "uf_acronym": "SP"}
        ],
    )

    sectionals = await client.sectionals(
        id=1, code="TEST", is_road_station=False, locality_id=100
    )

    assert len(sectionals) == 1
    assert sectionals[0] == Sectional(
        code="TEST", description="Test Station", id=1, uf_acronym="SP"
    )


async def test_get_road_travel_summary(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/summary/123",
        json=[
            {
                "arrival_zone": "SP",
                "departure_time_zone": "SP",
                "local_arrival_date_time": "2025-01-09T12:00:00",
                "local_exit": "2025-01-09T06:00:00",
                "seccional_code": "TEST",
                "seccional_id": 1,
                "seccional_name": "Test Station",
                "stop_time": "00:00",
                "total_time": "06:00",
                "total_km": 300.0,
                "uf_acronym": "SP",
            }
        ],
    )

    summary = await client.get_road_travel_summary(123)

    assert summary == [
        TravelLeg(
            arrival_zone="SP",
            departure_time_zone="SP",
            local_arrival_date_time="2025-01-09T12:00:00",
            local_exit="2025-01-09T06:00:00",
            seccional_code="TEST",
            seccional_id=1,
            seccional_name="Test Station",
            stop_time="00:00",
            total_time="06:00",
            total_km=300.0,
            uf_acronym="SP",
        )
    ]


async def test_get_road_travel_detail(httpx_mock: HTTPXMock, client: EulabsClient):
    httpx_mock.add_response(
        method="GET",
        url="https://eulabs.test/road/travels/detail/123",
        json=[
            {
                "origin_id": 1,
                "origin_name": "Origin City",
                "destination_id": 2,
                "destination_name": "Destination City",
                "class": "EXEC",
                "seats": 42,
                "capacity": 42,
                "class_description": "Executivo",
            }
        ],
    )

    details = await client.get_road_travel_detail(123)

    assert details == [
        TravelDetail(
            origin_id=1,
            origin_name="Origin City",
            destination_id=2,
            destination_name="Destination City",
            class_code="EXEC",
            seats=42,
            capacity=42,
            class_description="Executivo",
        )
    ]


async def test_travels_search_with_none_items(
    httpx_mock: HTTPXMock, client: EulabsClient
):
    httpx_mock.add_response(
        method="GET",
        url=(
            "https://eulabs.test/road/travels/search"
            "?departure_date=2025-01-09"
            "&origin_sectional_id=1001"
            "&destiny_sectional_id=2001"
        ),
        json=[
            {
                "key": "travel_key_123",
                "id": 1,
                "origin_sectional_id": 1001,
                "destiny_sectional_id": 2001,
                "datetime_departure": "2025-01-09T06:00:00Z",
                "datetime_arrival": "2025-01-09T12:00:00Z",
                "duration": "6:00",
                "price": 150.00,
                "price_promotional": 120.00,
                "free_seats": 38,
                "free_seats_benefits": None,
                "items": None,
            }
        ],
    )

    response = await client.travels_search(
        departure_date=date(2025, 1, 9),
        origin_sectional_id=1001,
        destiny_sectional_id=2001,
    )

    assert response == [
        Travel(
            key="travel_key_123",
            id=1,
            origin_sectional_id=1001,
            destiny_sectional_id=2001,
            datetime_departure="2025-01-09T06:00:00Z",
            datetime_arrival="2025-01-09T12:00:00Z",
            duration="6:00",
            price=150.00,
            price_promotional=120.00,
            free_seats=38,
            free_seats_benefits=None,
            items=None,
        )
    ]
