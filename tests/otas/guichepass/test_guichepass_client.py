from datetime import date

import pytest
from pytest_httpx import HTTPXMock

from marketplace.otas.guichepass.client import (
    AuthResponse,
    BusType,
    Checkpoint,
    GuichepassClient,
    Journey,
    JourneyInfo,
    Location,
    PriceInfo,
    Seat,
    TicketType,
)


@pytest.fixture
def client():
    return GuichepassClient(base_url="https://guichepass.test")


@pytest.fixture
def auth_response():
    return AuthResponse(
        accessToken="token123",
        refreshToken="refresh",
        accessTokenSso="sso",
        refreshTokenSso="refreshSso",
        expiresAt="2025-12-01T00:00:00Z",
        expiresSec=3600,
        currentDate="2025-06-24T00:00:00Z",
    )


async def test_auth_login(httpx_mock: HTTPXMock, client: GuichepassClient):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/auth/login",
        json={
            "accessToken": "token123",
            "refreshToken": "refresh",
            "accessTokenSso": "sso",
            "refreshTokenSso": "refreshSso",
            "expiresAt": "2025-12-01T00:00:00Z",
            "expiresSec": 3600,
            "currentDate": "2025-06-24T00:00:00Z",
        },
    )

    result = await client.auth_login("user", "pass")
    assert result == AuthResponse(
        accessToken="token123",
        refreshToken="refresh",
        accessTokenSso="sso",
        refreshTokenSso="refreshSso",
        expiresAt="2025-12-01T00:00:00Z",
        expiresSec=3600,
        currentDate="2025-06-24T00:00:00Z",
    )


async def test_locations(
    httpx_mock: HTTPXMock, client: GuichepassClient, auth_response: AuthResponse
):
    httpx_mock.add_response(
        method="GET",
        url="https://guichepass.test/web-sale/leg-stops/no-page",
        json=[
            {
                "id": 1,
                "name": "Terminal Rodoviário",
                "city": "Goiânia",
                "state": "GO",
                "ibgeCityCode": "5208707",
            }
        ],
    )

    result = await client.locations(auth_response)
    assert result == [
        Location(
            id=1,
            name="Terminal Rodoviário",
            city="Goiânia",
            state="GO",
            ibgeCityCode="5208707",
        )
    ]


async def test_journeys_search(
    httpx_mock: HTTPXMock, client: GuichepassClient, auth_response: AuthResponse
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/search",
        json=[
            {
                "origin": "1",
                "destination": "2",
                "departure": "2025-07-01T08:00",
                "arrival": "2025-07-01T12:00",
                "service": "Semi-Leito",
                "busCompany": "ViaNova",
                "busType": "C",
                "operationType": "NORMAL",
                "amenities": None,
                "distance": 350.0,
                "stopover": False,
                "freeSeats": 20,
                "price": 79.90,
                "kind": None,
                "message": "",
                "hasIntinerary": True,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0.0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "valueToAdd": None,
                "expirationDate": None,
                "inTransit": False,
            }
        ],
    )

    result = await client.journeys_search(
        auth_response, origin=1, destination=2, departure_date=date(2025, 7, 1)
    )
    assert result == [
        Journey(
            origin="1",
            destination="2",
            departure="2025-07-01T08:00",
            arrival="2025-07-01T12:00",
            service="Semi-Leito",
            busCompany="ViaNova",
            busType="C",
            operationType="NORMAL",
            amenities=None,
            distance=350.0,
            stopover=False,
            freeSeats=20,
            price=79.9,
            kind=None,
            message="",
            hasIntinerary=True,
            queryOnly=False,
            connection=False,
            noSeatNumberRequired=False,
            busId=None,
            discounts=[],
            companyDiscount=0.0,
            minAdvanceTime=None,
            minAdvanceTimeInMinutes=None,
            valueToAdd=None,
            expirationDate=None,
            inTransit=False,
        )
    ]


async def test_get_itinerary(
    httpx_mock: HTTPXMock, client: GuichepassClient, auth_response: AuthResponse
):
    httpx_mock.add_response(
        method="GET",
        url="https://guichepass.test/web-sale/service/SL123/itinerary",
        json=[{"id": 10, "name": "Uberlândia", "sequence": 1, "travelTime": "02:00"}],
    )

    result = await client.get_itinerary(auth_response, service="SL123")
    assert result == [
        Checkpoint(id=10, name="Uberlândia", sequence=1, travelTime="02:00")
    ]


async def test_get_bus_layout(
    httpx_mock: HTTPXMock, client: GuichepassClient, auth_response: AuthResponse
):
    httpx_mock.add_response(
        method="POST",
        url="https://guichepass.test/web-sale/bus-layout",
        json={
            "seats": [
                {
                    "status": "FREE",
                    "x": 0,
                    "y": 1,
                    "z": 0,
                    "number": "1A",
                    "description": "Poltrona comum",
                    "ticketType": [
                        {
                            "id": 1,
                            "description": "Passagem",
                            "salesStrategy": "DEFAULT",
                            "priceValue": 79.9,
                            "isNominalSale": False,
                            "allowsPromotion": False,
                            "code": "PAX",
                            "validateRule": None,
                            "numberSeat": None,
                            "amount": None,
                            "quotType": None,
                        }
                    ],
                }
            ],
            "travel": {
                "origin": "1",
                "destination": "2",
                "departure": "2025-07-01T08:00",
                "arrival": "2025-07-01T12:00",
                "service": "Semi-Leito",
                "busCompany": "ViaNova",
                "busType": "C",
                "operationType": "NORMAL",
                "amenities": None,
                "distance": 350.0,
                "stopover": False,
                "freeSeats": 20,
                "price": 79.90,
                "kind": None,
                "message": "",
                "hasIntinerary": True,
                "queryOnly": False,
                "connection": False,
                "noSeatNumberRequired": False,
                "busId": None,
                "discounts": [],
                "companyDiscount": 0.0,
                "minAdvanceTime": None,
                "minAdvanceTimeInMinutes": None,
                "valueToAdd": None,
                "expirationDate": None,
                "inTransit": False,
            },
            "priceInfo": {
                "basePrice": 70.0,
                "insurancePrice": 2.0,
                "taxPrice": 1.0,
                "otherPrice": 0.0,
                "tollPrice": 0.0,
                "boardingPrice": 1.0,
                "commission": 5.9,
                "companyDiscount": 0.0,
                "discounts": [],
                "cancelationFee": None,
                "price": 79.9,
                "totalDiscount": 0.0,
                "priceWithoutInsurance": 77.9,
                "totalCompanyDiscount": 0.0,
                "originalPriceWithoutInsurance": 77.9,
                "priceWithBusCompanyDiscount": 77.9,
                "priceWithInsurance": 79.9,
                "originalPrice": 79.9,
            },
            "listQuotas": [],
        },
    )

    response = await client.get_bus_layout(
        auth=auth_response,
        departure_date=date(2025, 7, 1),
        origin=1,
        destination=2,
        company_id="123",
        service="Semi-Leito",
    )

    assert response == JourneyInfo(
        seats=[
            Seat(
                status="FREE",
                x=0,
                y=1,
                z=0,
                number="1A",
                description="Poltrona comum",
                ticketType=[
                    TicketType(
                        id=1,
                        description="Passagem",
                        salesStrategy="DEFAULT",
                        priceValue=79.9,
                        isNominalSale=False,
                        allowsPromotion=False,
                        code="PAX",
                        validateRule=None,
                        numberSeat=None,
                        amount=None,
                        quotType=None,
                    )
                ],
            )
        ],
        travel=Journey(
            origin="1",
            destination="2",
            departure="2025-07-01T08:00",
            arrival="2025-07-01T12:00",
            service="Semi-Leito",
            busCompany="ViaNova",
            busType="C",
            operationType="NORMAL",
            amenities=None,
            distance=350.0,
            stopover=False,
            freeSeats=20,
            price=79.9,
            kind=None,
            message="",
            hasIntinerary=True,
            queryOnly=False,
            connection=False,
            noSeatNumberRequired=False,
            busId=None,
            discounts=[],
            companyDiscount=0.0,
            minAdvanceTime=None,
            minAdvanceTimeInMinutes=None,
            valueToAdd=None,
            expirationDate=None,
            inTransit=False,
        ),
        priceInfo=PriceInfo(
            basePrice=70.0,
            insurancePrice=2.0,
            taxPrice=1.0,
            otherPrice=0.0,
            tollPrice=0.0,
            boardingPrice=1.0,
            commission=5.9,
            companyDiscount=0.0,
            discounts=[],
            cancelationFee=None,
            price=79.9,
            totalDiscount=0.0,
            priceWithoutInsurance=77.9,
            totalCompanyDiscount=0.0,
            originalPriceWithoutInsurance=77.9,
            priceWithBusCompanyDiscount=77.9,
            priceWithInsurance=79.9,
            originalPrice=79.9,
        ),
        listQuotas=[],
    )


async def test_get_bus_types(
    httpx_mock: HTTPXMock, client: GuichepassClient, auth_response: AuthResponse
):
    httpx_mock.add_response(
        method="GET",
        url="https://guichepass.test/web-sale/seatTypes",
        json=[{"name": "Leito", "id": 1}],
    )

    result = await client.get_bus_types(auth_response)
    assert result == [BusType(name="Leito", id=1)]
