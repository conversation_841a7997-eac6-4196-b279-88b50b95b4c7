from datetime import date, datetime, timezone
from unittest import mock
from unittest.mock import AsyncMock, MagicMock

import pytest

from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTACompany,
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTASearchCacheConfig,
    OTASearchCircuitBreakerConfig,
    OTASeatType,
    OTASeatTypeStatus,
    Place,
    Seat,
    Travel,
)
from marketplace.otas.guichepass.client import (
    BusType,
    GuichepassClient,
    Journey,
    JourneyInfo,
    Location,
    PriceInfo,
    TicketType,
)
from marketplace.otas.guichepass.client import (
    Checkpoint as GuicheCheckpoint,
)
from marketplace.otas.guichepass.client import (
    Seat as GuichepassSeat,
)
from marketplace.otas.guichepass.searcher import (
    GuichepassSearcher,
)


@pytest.fixture
def ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=3,
        name="Guichepass",
        provider="guichepass",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
        companies=[
            OTACompany(
                name="Guiche Company",
                external_id=555,
                cnpj="12345678000100",
                created_at=now,
                ota_config_id=3,
            )
        ],
        seat_types=[
            OTASeatType(
                ota_config_id=3,
                name="Convencional",
                status=OTASeatTypeStatus.AVAILABLE,
                seat_type="convencional",
                created_at=now,
                updated_at=now,
            )
        ],
        search_cache=OTASearchCacheConfig(ttl=86400, stale_after=900),
        circuit_breaker=OTASearchCircuitBreakerConfig(enabled=False),
        config={
            "username": "testuser",
            "password": "testpass",
        },
    )


@pytest.fixture
def mock_client():
    client = AsyncMock(spec=GuichepassClient)
    client.auth_login.return_value = MagicMock(
        accessToken="token",
        expiresSec=3600,
        accessTokenSso="",
        refreshToken="",
        refreshTokenSso="",
        expiresAt="",
        currentDate="",
    )
    return client


@pytest.fixture
def searcher(ota_config, mock_client):
    searcher = GuichepassSearcher(
        ota_config=ota_config,
        base_url="https://guichepass.test",
        client_id="WEB_SALE",
        username="testuser",
        password="testpass",
    )
    searcher._session = mock_client
    return searcher


async def test_search(searcher, mock_client):
    mock_client.journeys_search.return_value = [
        Journey(
            origin="1001",
            destination="2001",
            departure="2025-06-22T01:30:00",
            arrival="2025-06-22T07:00:00",
            service="GP123",
            busCompany="555",
            busType="1",
            operationType="normal",
            amenities=None,
            distance=None,
            stopover=False,
            freeSeats=32,
            price=120.0,
            kind=None,
            message="",
            hasIntinerary=True,
            queryOnly=False,
            connection=False,
            noSeatNumberRequired=False,
            busId=None,
            discounts=[],
            companyDiscount=0.0,
            minAdvanceTime=None,
            minAdvanceTimeInMinutes=None,
            valueToAdd=None,
            expirationDate=None,
            inTransit=None,
        )
    ]

    mock_client.get_bus_types.return_value = [
        BusType(id=1, name="Convencional"),
    ]

    origin = OTAPlace(
        ota_config_id=3,
        name="Origem",
        place=Place(id=1, name="Origem", slug="origem"),
        extra={"id": 1001},
    )
    destination = OTAPlace(
        ota_config_id=3,
        name="Destino",
        place=Place(id=2, name="Destino", slug="destino"),
        extra={"id": 2001},
    )

    results = await searcher.search(
        origin=origin,
        destination=destination,
        departure_date=date(2025, 1, 9),
    )

    assert len(results) == 1
    expected = Travel(
        ota="guichepass",
        ota_config_id=3,
        code="GP123_1001_2001_GP123_555",
        service_code="GP123",
        company=OTACompany(
            ota_config_id=3,
            external_id=555,
            name="Guiche Company",
            cnpj="12345678000100",
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        itinerary=[],
        origin=Place(id=1, name="Origem", slug="origem"),
        destination=Place(id=2, name="Destino", slug="destino"),
        departure_at=datetime(2025, 6, 22, 1, 30),
        arrival_at=datetime(2025, 6, 22, 7, 0),
        seat_type=OTASeatType(
            ota_config_id=3,
            name="Convencional",
            seat_type="convencional",
            status=OTASeatTypeStatus.AVAILABLE,
            created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
            updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        ),
        price=120.0,
        available_seats=32,
        total_seats=32,
        last_synced=mock.ANY,
        extra={
            "origin": "1001",
            "destination": "2001",
            "departure": "2025-06-22T01:30:00",
            "busCompany": "555",
            "service": "GP123",
        },
        single_ticket_connection=False,
        stopovers=None,
    )
    assert results[0] == expected


async def test_available_seats(searcher, mock_client):
    mock_client.get_bus_layout.return_value = JourneyInfo(
        seats=[
            GuichepassSeat(
                status="FREE",
                x=0,
                y=1,
                z=0,
                number="1A",
                description="Convencional",
                ticketType=[
                    TicketType(
                        id=1,
                        description="Passagem",
                        salesStrategy="DEFAULT",
                        priceValue=79.9,
                        isNominalSale=False,
                        allowsPromotion=False,
                        code="PAX",
                        validateRule=None,
                        numberSeat=None,
                        amount=None,
                        quotType=None,
                    )
                ],
            )
        ],
        travel=Journey(
            origin="1",
            destination="2",
            departure="2025-07-01T08:00",
            arrival="2025-07-01T12:00",
            service="Semi-Leito",
            busCompany="ViaNova",
            busType="C",
            operationType="NORMAL",
            amenities=None,
            distance=350.0,
            stopover=False,
            freeSeats=20,
            price=79.9,
            kind=None,
            message="",
            hasIntinerary=True,
            queryOnly=False,
            connection=False,
            noSeatNumberRequired=False,
            busId=None,
            discounts=[],
            companyDiscount=0.0,
            minAdvanceTime=None,
            minAdvanceTimeInMinutes=None,
            valueToAdd=None,
            expirationDate=None,
            inTransit=False,
        ),
        priceInfo=PriceInfo(
            basePrice=70.0,
            insurancePrice=2.0,
            taxPrice=1.0,
            otherPrice=0.0,
            tollPrice=0.0,
            boardingPrice=1.0,
            commission=5.9,
            companyDiscount=0.0,
            discounts=[],
            cancelationFee=None,
            price=79.9,
            totalDiscount=0.0,
            priceWithoutInsurance=77.9,
            totalCompanyDiscount=0.0,
            originalPriceWithoutInsurance=77.9,
            priceWithBusCompanyDiscount=77.9,
            priceWithInsurance=79.9,
            originalPrice=79.9,
        ),
        listQuotas=[],
    )

    travel = InputTravel(
        ota_config_id=3,
        extra={
            "origin": "1001",
            "destination": "2001",
            "departure": "2025-06-22T01:30:00",
            "busCompany": "555",
            "service": "GP123",
        },
    )

    seats = await searcher.available_seats(travel)

    assert seats == [
        Seat(
            number="1A",
            floor=1,
            row=1,
            column=2,
            available=True,
            category="Convencional",
            seat_type="convencional",
            price=79.9,
            extra={"description": "Convencional"},
        )
    ]


async def test_travel_itinerary(searcher, mock_client):
    mock_client.get_itinerary.return_value = [
        GuicheCheckpoint(
            id=1, name="Primeiro Ponto", sequence=1, travelTime="00:00:00"
        ),
        GuicheCheckpoint(id=1001, name="Origem", sequence=2, travelTime="02:00:00"),
        GuicheCheckpoint(id=2001, name="Destino", sequence=3, travelTime="05:30:00"),
        GuicheCheckpoint(
            id=3001, name="Ultimo Ponto", sequence=3, travelTime="05:30:00"
        ),
    ]

    travel = InputTravel(
        ota_config_id=3,
        extra={
            "origin": "1001",
            "destination": "2001",
            "departure": "2025-06-22T01:30:00",
            "busCompany": "555",
            "service": "GP123",
        },
    )

    result = await searcher.travel_itinerary(travel)

    assert result == [
        Checkpoint(
            name="Origem", departure_at=datetime(2025, 6, 22, 1, 30), distance_km=0.0
        ),
        Checkpoint(
            name="Destino", departure_at=datetime(2025, 6, 22, 5, 0), distance_km=210.0
        ),
    ]


async def test_list_places(searcher, mock_client):
    locations = [
        Location(
            id=1001,
            name="Rodoviária Central",
            city="Goiânia",
            state="GO",
            ibgeCityCode="5208707",
        ),
        Location(
            id=2001,
            name="Terminal Novo",
            city="Anápolis",
            state="GO",
            ibgeCityCode="5201108",
        ),
    ]

    mock_client.locations.return_value = locations

    places = await searcher.list_places()
    assert set(places) == set(
        [
            OTAPlace(
                ota_config_id=searcher.ota_config.id,
                name="Rodoviária Central - GO",
                extra=locations[0],
            ),
            OTAPlace(
                ota_config_id=searcher.ota_config.id,
                name="Terminal Novo - GO",
                extra=locations[1],
            ),
        ]
    )
