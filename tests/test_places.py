from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch

import pytest

from marketplace.models import (
    OTAConfig,
    OTAConfigStatus,
    OTAPlace,
    OTAPlaceStatus,
)
from marketplace.places import (
    refresh_all_ota_places,
    refresh_ota_places,
    slug_to_ltree,
)


@pytest.fixture
def mock_db_conn():
    return AsyncMock()


@pytest.fixture
def sample_ota_config():
    now = datetime(2024, 1, 1, tzinfo=timezone.utc)
    return OTAConfig(
        id=1,
        name="Test OTA",
        provider="test_provider",
        status=OTAConfigStatus.active,
        created_at=now,
        updated_at=now,
    )


@pytest.fixture
def sample_ota_places():
    return [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp_001"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj_001"},
            status=OTAPlaceStatus.USER,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Belo Horizonte",
            extra={"id": "bh_001"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]


@pytest.fixture
def sample_fetched_places():
    return [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp_001"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Brasília",
            extra={"id": "bsb_001"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj_001"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]


@pytest.fixture
def sample_places_with_subplaces():
    return [
        {"slug": "sao_paulo", "subplaces": ["sao_paulo_centro"]},
        {"slug": "rio_de_janeiro", "subplaces": ["rio_de_janeiro_centro"]},
        {"slug": "brasilia", "subplaces": []},
    ]


@patch("marketplace.places.database.queries.list_places_with_subplaces")
@patch("marketplace.places.list_ota_places")
@patch("marketplace.places.fetch_ota_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.database.queries.insert_ota_config_places")
async def test_refresh_ota_places_with_system_and_new_places(
    mock_insert_places,
    mock_find_matching,
    mock_fetch_places,
    mock_list_places,
    mock_list_subplaces,
    mock_db_conn,
    sample_ota_config,
    sample_ota_places,
    sample_fetched_places,
    sample_places_with_subplaces,
):
    mock_list_subplaces.return_value = sample_places_with_subplaces
    mock_list_places.return_value = sample_ota_places
    mock_fetch_places.return_value = sample_fetched_places
    mock_find_matching.return_value = [
        (sample_fetched_places[0], "sao-paulo"),
        (sample_fetched_places[1], "brasilia"),
        (sample_fetched_places[2], None),
    ]
    mock_insert_places.return_value = None

    await refresh_ota_places(mock_db_conn, sample_ota_config)

    mock_list_subplaces.assert_called_once_with(mock_db_conn)
    mock_list_places.assert_called_once_with(mock_db_conn, config_id=1)
    mock_fetch_places.assert_called_once_with(sample_ota_config)

    mock_find_matching.assert_called_once()
    called_places = mock_find_matching.call_args[0][0]
    assert len(called_places) == 2
    assert sample_fetched_places[0] in called_places
    assert sample_fetched_places[1] in called_places
    assert sample_fetched_places[2] not in called_places

    mock_insert_places.assert_called_once()
    insert_data = mock_insert_places.call_args[0][1]
    assert len(insert_data) == 3

    sp_entry = next(item for item in insert_data if item["name"] == "São Paulo")
    assert sp_entry["place"] == slug_to_ltree("sao_paulo_centro")

    bsb_entry = next(item for item in insert_data if item["name"] == "Brasília")
    assert bsb_entry["place"] == slug_to_ltree("brasilia")

    rj_entry = next(item for item in insert_data if item["name"] == "Rio de Janeiro")
    assert rj_entry["place"] is None


@patch("marketplace.places.database.queries.list_places_with_subplaces")
@patch("marketplace.places.list_ota_places")
@patch("marketplace.places.fetch_ota_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.database.queries.insert_ota_config_places")
async def test_refresh_ota_places_with_subplaces_selection(
    mock_insert_places,
    mock_find_matching,
    mock_fetch_places,
    mock_list_places,
    mock_list_subplaces,
    mock_db_conn,
    sample_ota_config,
):
    mock_list_subplaces.return_value = [
        {"slug": "sao_paulo", "subplaces": ["sao_paulo_centro"]},
        {"slug": "rio_de_janeiro", "subplaces": ["rio_centro", "rio_zona_sul"]},
    ]
    mock_list_places.return_value = []

    fetched_places = [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]
    mock_fetch_places.return_value = fetched_places

    mock_find_matching.return_value = [
        (fetched_places[0], "sao-paulo"),
        (fetched_places[1], "rio-de-janeiro"),
    ]
    mock_insert_places.return_value = None

    await refresh_ota_places(mock_db_conn, sample_ota_config)

    mock_insert_places.assert_called_once()
    insert_data = mock_insert_places.call_args[0][1]

    sp_entry = next(item for item in insert_data if item["name"] == "São Paulo")
    assert sp_entry["place"] == slug_to_ltree("sao_paulo_centro")

    rj_entry = next(item for item in insert_data if item["name"] == "Rio de Janeiro")
    assert rj_entry["place"] == slug_to_ltree("rio-de-janeiro")


@patch("marketplace.places.database.queries.list_places_with_subplaces")
@patch("marketplace.places.list_ota_places")
@patch("marketplace.places.fetch_ota_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.database.queries.insert_ota_config_places")
async def test_refresh_ota_places_excludes_user_managed_places(
    mock_insert_places,
    mock_find_matching,
    mock_fetch_places,
    mock_list_places,
    mock_list_subplaces,
    mock_db_conn,
    sample_ota_config,
):
    mock_list_subplaces.return_value = [
        {"slug": "brasilia", "subplaces": []},
    ]

    current_places = [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp"},
            status=OTAPlaceStatus.USER,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj"},
            status=OTAPlaceStatus.IGNORED,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Brasília",
            extra={"id": "bsb"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]
    mock_list_places.return_value = current_places

    fetched_places = [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Brasília",
            extra={"id": "bsb"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]
    mock_fetch_places.return_value = fetched_places

    mock_find_matching.return_value = [
        (fetched_places[2], "brasilia"),
    ]
    mock_insert_places.return_value = None

    await refresh_ota_places(mock_db_conn, sample_ota_config)

    mock_find_matching.assert_called_once()
    called_places = mock_find_matching.call_args[0][0]

    assert len(called_places) == 1
    assert called_places[0].name == "Brasília"

    place_names = [p.name for p in called_places]
    assert "São Paulo" not in place_names
    assert "Rio de Janeiro" not in place_names


@patch("marketplace.places.database.queries.list_places_with_subplaces")
@patch("marketplace.places.list_ota_configs")
@patch("marketplace.places.list_ota_places")
@patch("marketplace.places.fetch_all_ota_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.database.queries.insert_ota_config_places")
async def test_refresh_all_ota_places_multiple_configs(
    mock_insert_places,
    mock_find_matching,
    mock_fetch_all_places,
    mock_list_places,
    mock_list_configs,
    mock_list_subplaces,
    mock_db_conn,
):
    config1 = OTAConfig(
        id=1,
        name="OTA1",
        provider="provider1",
        status=OTAConfigStatus.active,
        created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )
    config2 = OTAConfig(
        id=2,
        name="OTA2",
        provider="provider2",
        status=OTAConfigStatus.active,
        created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )

    mock_list_subplaces.return_value = [
        {"slug": "sao_paulo", "subplaces": ["sao_paulo_centro"]},
        {"slug": "curitiba", "subplaces": []},
        {"slug": "brasilia", "subplaces": []},
    ]
    mock_list_configs.return_value = [config1, config2]

    current_places_config1 = [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp1"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj1"},
            status=OTAPlaceStatus.USER,
        ),
    ]
    current_places_config2 = [
        OTAPlace(
            ota_config_id=2,
            name="Brasília",
            extra={"id": "bsb2"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]

    def mock_list_places_side_effect(_, config_id):
        if config_id == 1:
            return current_places_config1
        elif config_id == 2:
            return current_places_config2
        return []

    mock_list_places.side_effect = mock_list_places_side_effect

    fetched_places = [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp1"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj1"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Curitiba",
            extra={"id": "cwb1"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=2,
            name="Brasília",
            extra={"id": "bsb2"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=2,
            name="Goiânia",
            extra={"id": "gyn2"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]
    mock_fetch_all_places.return_value = fetched_places

    mock_find_matching.return_value = [
        (fetched_places[0], "sao-paulo"),
        (fetched_places[2], "curitiba"),
        (fetched_places[3], "brasilia"),
        (fetched_places[4], None),
    ]
    mock_insert_places.return_value = None

    await refresh_all_ota_places(mock_db_conn)

    mock_list_configs.assert_called_once_with(mock_db_conn)
    assert mock_list_places.call_count == 2
    mock_fetch_all_places.assert_called_once()

    mock_find_matching.assert_called_once()
    called_places = mock_find_matching.call_args[0][0]

    assert len(called_places) == 4
    place_names = [p.name for p in called_places]
    assert "São Paulo" in place_names
    assert "Curitiba" in place_names
    assert "Brasília" in place_names
    assert "Goiânia" in place_names
    assert "Rio de Janeiro" not in place_names

    mock_insert_places.assert_called_once()
    insert_data = mock_insert_places.call_args[0][1]
    assert len(insert_data) == 4


@patch("marketplace.places.database.queries.list_places_with_subplaces")
@patch("marketplace.places.list_ota_configs")
@patch("marketplace.places.list_ota_places")
@patch("marketplace.places.fetch_all_ota_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.database.queries.insert_ota_config_places")
async def test_refresh_all_ota_places_empty_configs(
    mock_insert_places,
    mock_find_matching,
    mock_fetch_all_places,
    mock_list_places,
    mock_list_configs,
    mock_list_subplaces,
    mock_db_conn,
):
    mock_list_subplaces.return_value = []
    mock_list_configs.return_value = []
    mock_list_places.return_value = []
    mock_fetch_all_places.return_value = []
    mock_find_matching.return_value = []
    mock_insert_places.return_value = None

    await refresh_all_ota_places(mock_db_conn)

    mock_list_configs.assert_called_once_with(mock_db_conn)
    mock_list_places.assert_not_called()
    mock_fetch_all_places.assert_called_once()
    mock_find_matching.assert_called_once_with([], [])
    mock_insert_places.assert_called_once_with(mock_db_conn, [])


@patch("marketplace.places.database.queries.list_places_with_subplaces")
@patch("marketplace.places.list_ota_configs")
@patch("marketplace.places.list_ota_places")
@patch("marketplace.places.fetch_all_ota_places")
@patch("marketplace.places.find_matching_places")
@patch("marketplace.places.database.queries.insert_ota_config_places")
async def test_refresh_all_ota_places_only_new_places(
    mock_insert_places,
    mock_find_matching,
    mock_fetch_all_places,
    mock_list_places,
    mock_list_configs,
    mock_list_subplaces,
    mock_db_conn,
):
    config = OTAConfig(
        id=1,
        name="OTA1",
        provider="provider1",
        status=OTAConfigStatus.active,
        created_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
        updated_at=datetime(2024, 1, 1, tzinfo=timezone.utc),
    )

    mock_list_subplaces.return_value = [
        {"slug": "sao_paulo", "subplaces": []},
        {"slug": "rio_de_janeiro", "subplaces": []},
    ]
    mock_list_configs.return_value = [config]
    mock_list_places.return_value = []

    fetched_places = [
        OTAPlace(
            ota_config_id=1,
            name="São Paulo",
            extra={"id": "sp"},
            status=OTAPlaceStatus.SYSTEM,
        ),
        OTAPlace(
            ota_config_id=1,
            name="Rio de Janeiro",
            extra={"id": "rj"},
            status=OTAPlaceStatus.SYSTEM,
        ),
    ]
    mock_fetch_all_places.return_value = fetched_places

    mock_find_matching.return_value = [
        (fetched_places[0], "sao-paulo"),
        (fetched_places[1], "rio-de-janeiro"),
    ]
    mock_insert_places.return_value = None

    await refresh_all_ota_places(mock_db_conn)

    mock_find_matching.assert_called_once()
    called_places = mock_find_matching.call_args[0][0]

    assert len(called_places) == 2
    assert all(p in called_places for p in fetched_places)

    mock_insert_places.assert_called_once()
    insert_data = mock_insert_places.call_args[0][1]
    assert len(insert_data) == 2
