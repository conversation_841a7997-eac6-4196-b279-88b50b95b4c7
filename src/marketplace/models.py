from dataclasses import dataclass, field
from datetime import date, datetime
from enum import StrEnum
from functools import cached_property
from typing import Any, Optional, Type, TypeVar

import dacite


class OTAConfigStatus(StrEnum):
    active = "active"
    paused = "paused"


@dataclass
class OTASearchCacheConfig:
    ttl: int = 900
    stale_after: int = 600
    few_seats_threshold: int = 5
    ttl_few_seats: int = 60
    ttl_not_available: int = 24 * 60 * 60
    max_empty_responses: int = 3
    empty_responses_window: int = 24 * 60 * 60


@dataclass
class OTASearchCircuitBreakerConfig:
    failure_threshold: int = 10
    failure_window: int = 60
    recovery_timeout: int = 30
    recovery_test_requests: int = 1
    enabled: bool = False


@dataclass
class ReducedOTAConfig:
    id: int
    name: str
    companies: Optional[list["OTACompany"]] = field(default_factory=list)


@dataclass
class OTAConfig:
    id: int
    name: str
    provider: str
    status: OTAConfigStatus
    created_at: Optional[datetime]
    updated_at: Optional[datetime]
    config: dict[str, str | int | float] = field(default_factory=dict)
    search_cache: OTASearchCacheConfig = field(default_factory=OTASearchCacheConfig)
    circuit_breaker: OTASearchCircuitBreakerConfig = field(
        default_factory=OTASearchCircuitBreakerConfig
    )
    seat_types: Optional[list["OTASeatType"]] = field(default_factory=list)
    companies: Optional[list["OTACompany"]] = field(default_factory=list)

    def __repr__(self):
        return f"OTAConfig(id={self.id}, name={self.name}, provider={self.provider})"

    def __eq__(self, other) -> bool:
        if isinstance(other, OTAConfig):
            return (
                self.companies_mapping == other.companies_mapping
                and self.seat_types_mapping == other.seat_types_mapping
                and self.config == other.config
                and self.search_cache == other.search_cache
                and self.circuit_breaker == other.circuit_breaker
            )
        return False

    @cached_property
    def seat_types_mapping(self) -> dict[str, "OTASeatType"]:
        if self.seat_types is None:
            return {}
        return {s.name: s for s in self.seat_types}

    @cached_property
    def companies_mapping(self) -> dict[int, "OTACompany"]:
        if self.companies is None:
            return {}
        return {s.external_id: s for s in self.companies}

    def reduced(self) -> ReducedOTAConfig:
        return ReducedOTAConfig(id=self.id, name=self.name, companies=self.companies)


class OTAPlaceStatus(StrEnum):
    SYSTEM = "system"  # system managed
    USER = "user"  # user managed
    IGNORED = "ignored"


@dataclass
class OTAPlace:
    ota_config_id: int
    name: str
    extra: Any = field(repr=False)
    status: OTAPlaceStatus = OTAPlaceStatus.SYSTEM
    place: Optional["Place"] = None
    created_at: Optional[datetime] = None

    def __repr__(self):
        return f"OTAPlace(name={self.name})"

    def __hash__(self):
        return hash((self.ota_config_id, self.name))

    def __eq__(self, other):
        if isinstance(other, OTAPlace):
            return (self.ota_config_id, self.name) == (other.ota_config_id, other.name)
        return False

    @property
    def ota_place_id(self):
        # TODO - Dar uma padronizada no que é esperado do extra por OTA
        return self.extra.get("id") or self.extra["IdLocalidade"]


class OTASeatTypeStatus(StrEnum):
    AVAILABLE = "available"
    NOT_LISTED = "not-listed"


@dataclass
class OTASeatType:
    ota_config_id: int
    name: str
    status: OTASeatTypeStatus
    seat_type: Optional[str]
    created_at: Optional[datetime]
    updated_at: Optional[datetime]

    def __eq__(self, other):
        if isinstance(other, OTASeatType):
            return (self.ota_config_id, self.name, self.status, self.seat_type) == (
                other.ota_config_id,
                other.name,
                self.status,
                other.seat_type,
            )
        return False


@dataclass
class OTACompany:
    ota_config_id: int
    external_id: int
    name: Optional[str]
    cnpj: Optional[str]
    created_at: Optional[datetime]

    def __eq__(self, other):
        if isinstance(other, OTACompany):
            return (self.ota_config_id, self.external_id, self.cnpj) == (
                other.ota_config_id,
                other.external_id,
                other.cnpj,
            )
        return False


@dataclass
class InputTravel:
    ota_config_id: int
    extra: dict[str, Any]


@dataclass
class Travel:
    ota: str
    ota_config_id: int
    code: str  #: Unique code for travel
    service_code: str
    company: Optional[OTACompany]

    itinerary: list["Checkpoint"]

    origin: "Place"
    departure_at: datetime

    destination: "Place"
    arrival_at: datetime

    seat_type: OTASeatType
    available_seats: Optional[int]
    total_seats: int

    last_synced: datetime

    extra: Any  #: additional information, OTA specific, must be json-serializable

    single_ticket_connection: bool = (
        False  # indicates that the OTA is responsible for issuing connecting tickets
    )
    stopovers: Optional[list["Stopover"]] | None = None
    price: Optional[float] = None


@dataclass
class Stopover:
    origin_ota_place_id: int
    destination_ota_place_id: int
    extra: Any = None


@dataclass
class SearchResult:
    ota_config: ReducedOTAConfig
    origin: OTAPlace
    destination: OTAPlace
    departure_date: date
    travels: list[Travel] | None
    error: Optional[str] = None


@dataclass
class Checkpoint:
    name: str
    departure_at: datetime
    distance_km: Optional[float] = None


@dataclass
class Seat:
    number: str
    available: bool
    category: str
    seat_type: str | None
    floor: int
    row: int
    column: int
    extra: Any  # additional information, OTA specific, must be json-serializable
    price: Optional[float] = None
    block_key: Optional[str] = None


@dataclass
class Pax:
    name: str
    doc: str
    phone: str
    birthdate: date


class PlaceStatus(StrEnum):
    ACTIVE = "active"
    INACTIVE = "inactive"


@dataclass
class Place:
    slug: str
    name: str
    id: Optional[int] = None
    status: PlaceStatus = PlaceStatus.ACTIVE
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    def __hash__(self):
        return hash(self.slug)  # slug is unique


DEFAULT_DACITE_CONFIG = dacite.Config(
    cast=[StrEnum],
    type_hooks={
        datetime: lambda v: v if isinstance(v, datetime) else datetime.fromisoformat(v),
        date: lambda v: v if isinstance(v, date) else date.fromisoformat(v),
        list[OTASeatType]: lambda items: [dict(item) for item in items],
        list[OTACompany]: lambda items: [dict(item) for item in items],
    },
)

T = TypeVar("T")


def from_dict(
    data_class: Type[T],
    data: Any,
    config: Optional[dacite.Config] = DEFAULT_DACITE_CONFIG,
) -> T:
    return dacite.from_dict(data_class, data, config)
