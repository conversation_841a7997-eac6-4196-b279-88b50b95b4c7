from dataclasses import dataclass
from datetime import date

from marketplace.otas.http import AsyncClient


@dataclass
class Travel:
    id: int
    origin: int
    destination: int
    departure: str
    arrival: str
    service: str
    busCompany: str
    busCompanyName: str
    freeSeats: int
    price: float
    toll: float
    busType: str
    message: str

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            origin=raw["origin"],
            destination=raw["destination"],
            departure=raw["departure"],
            arrival=raw["arrival"],
            service=raw["service"],
            busCompany=raw["busCompany"],
            busCompanyName=raw["busCompanyName"],
            freeSeats=raw["freeSeats"],
            price=raw["price"],
            toll=raw["toll"],
            busType=raw["busType"],
            message=raw["message"],
        )


@dataclass
class Checkpoint:
    id: int
    localId: int
    localNome: str
    localUf: str
    dataHoraPartida: str
    totalKm: float
    duracao: str
    tz: int

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            localId=raw["localId"],
            localNome=raw["localNome"],
            localUf=raw["localUf"],
            dataHoraPartida=raw["dataHoraPartida"],
            totalKm=raw["totalKm"],
            duracao=raw["duracao"],
            tz=raw["tz"],
        )


@dataclass
class Seat:
    status: str
    x: int
    y: int
    number: str
    description: str | None

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            status=raw["status"],
            x=raw["x"],
            y=raw["y"],
            number=raw["number"],
            description=raw["description"],
        )


@dataclass
class Deck:
    number: int
    seats: list[Seat]

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            number=raw["number"], seats=[Seat.from_dict(seat) for seat in raw["seats"]]
        )


@dataclass
class BusLayout:
    decks: list[Deck]

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(decks=[Deck.from_dict(deck) for deck in raw["decks"]])


@dataclass
class PriceInfo:
    basePrice: float
    insurancePrice: float
    taxPrice: float
    otherPrice: float
    tollPrice: float
    boardingPrice: float
    comission: float
    price: float

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            basePrice=raw["basePrice"],
            insurancePrice=raw["insurancePrice"],
            taxPrice=raw["taxPrice"],
            otherPrice=raw["otherPrice"],
            tollPrice=raw["tollPrice"],
            boardingPrice=raw["boardingPrice"],
            comission=raw["comission"],
            price=raw["price"],
        )


@dataclass
class TravelInfo:
    travel: Travel
    priceInfo: PriceInfo
    busLayout: BusLayout

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            travel=Travel.from_dict(raw["travel"]),
            priceInfo=PriceInfo.from_dict(raw["priceInfo"]),
            busLayout=BusLayout.from_dict(raw["busLayout"]),
        )


class TiSistemasClient:
    def __init__(
        self,
        base_url: str,
        auth_token: str,
        timeout: int = 20,
        log_prefix: str | None = None,
    ):
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            headers={"I-Auth": f"Bearer {auth_token}"},
            log_prefix=f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__,
        )

    async def travels_search(
        self, origin: int, destination: int, departure_date: date
    ) -> list[Travel]:
        response = await self._client.get(
            "/rest/bs/travels",
            params={
                "origin": origin,
                "destination": destination,
                "date": departure_date.strftime("%d-%m-%Y"),
            },
        )

        response.raise_for_status()
        return [Travel.from_dict(t) for t in response.json()]

    async def get_itinerary(self, company_id: str, travel_id: str) -> list[Checkpoint]:
        response = await self._client.get(
            f"/rest/bs/itineraries/{company_id}/{travel_id}"
        )

        response.raise_for_status()
        return [Checkpoint.from_dict(t) for t in response.json()["list"]]

    async def get_bus_layout(
        self,
        departure_date: date,
        origin: int,
        destination: int,
        company_id: str,
        id: int,
    ) -> TravelInfo:
        response = await self._client.get(
            "/rest/bs/bus",
            params={
                "date": departure_date.strftime("%d-%m-%Y"),
                "origin": origin,
                "destination": destination,
                "busCompany": company_id,
                "id": id,
            },
        )

        response.raise_for_status()
        return TravelInfo.from_dict(response.json())
