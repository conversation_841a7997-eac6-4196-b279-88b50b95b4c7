import asyncio
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from typing import cast, override

import dacite
import httpx
from tenacity import (
    retry,
    retry_if_not_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    Travel,
)
from marketplace.otas.exception import OTASearcherException, OTASearcherTimeoutException
from marketplace.otas.ti_sistemas.client import (
    Checkpoint as TiSistemasCheckpoint,
)
from marketplace.otas.ti_sistemas.client import TiSistemasClient, TravelInfo
from marketplace.otas.ti_sistemas.client import (
    Travel as TiSistemasTravel,
)
from marketplace.searcher import OTASearcher


@dataclass
class TiSistemasTravelExtra:
    id: int
    origin: int
    destination: int
    departure: str
    busCompany: str
    service: str


class TiSistemasSearcher(OTASearcher):
    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        auth_token: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._session = TiSistemasClient(
            base_url=base_url, auth_token=auth_token, log_prefix=self.ota_config.name
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_not_exception_type(dacite.WrongTypeError),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        travels, last_synced, _ = await self._search_travels(
            departure_date=departure_date,
            origin=origin.extra["id"],
            destination=destination.extra["id"],
        )
        travels = cast(list[TiSistemasTravel], travels)

        found = []
        for travel in travels:
            company = self._get_ota_company(
                int(travel.busCompany), name=travel.busCompanyName
            )
            seat_type = self._get_ota_seat_type_by_name(travel.service)
            if company is None or seat_type is None:
                continue

            found.append(
                Travel(
                    ota="ti_sistemas",
                    ota_config_id=self.ota_config.id,
                    code=f"{travel.id}_{travel.origin}_{travel.destination}_{travel.service}_{travel.busCompany}",
                    service_code=str(travel.id),
                    company=company,
                    itinerary=[],
                    origin=cast(Place, origin.place),
                    destination=cast(Place, destination.place),
                    departure_at=datetime.fromisoformat(travel.departure),
                    arrival_at=datetime.fromisoformat(travel.arrival),
                    seat_type=seat_type,
                    price=travel.price,
                    available_seats=travel.freeSeats,
                    total_seats=travel.freeSeats,
                    last_synced=last_synced,
                    extra=asdict(
                        TiSistemasTravelExtra(
                            id=travel.id,
                            origin=travel.origin,
                            destination=travel.destination,
                            departure=travel.departure,
                            busCompany=travel.busCompany,
                            service=travel.service,
                        )
                    ),
                )
            )

        return found

    def _has_few_seats_travel(self, search_response: list[TiSistemasTravel]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for travel in search_response:
            if travel.freeSeats < threshold:
                return True

        return False

    @override
    async def available_seats(self, travel: InputTravel) -> list[Seat]:
        seats = []
        travel_info = await self._seating_map(
            departure_date=datetime.fromisoformat(travel.extra["departure"]),
            origin=travel.extra["origin"],
            destination=travel.extra["destination"],
            company_id=travel.extra["busCompany"],
            id=travel.extra["id"],
        )
        travel_info = cast(TravelInfo, travel_info)
        seat_type = self._get_ota_seat_type_by_name(travel.extra["service"])
        for deck in travel_info.busLayout.decks:
            for seat in deck.seats:
                seats.append(
                    Seat(
                        number=seat.number,
                        floor=deck.number,
                        row=seat.x,
                        column=seat.y,
                        available=seat.status == "FREE",
                        category=travel.extra["service"],
                        seat_type=seat_type.seat_type if seat_type else None,
                        price=None,
                        extra={"description": seat.description},
                    )
                )

        return seats

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        travel_extra = TiSistemasTravelExtra(**travel.extra)
        itinerary: list[Checkpoint] = []
        last_total_km = 0
        post_origin_checkpoint = False
        departure = datetime.fromisoformat(travel_extra.departure)
        travel_id = f"{departure.strftime('%Y%m%d')}{travel_extra.id}"
        full_itinerary = await self._travel_itinerary(
            travel_extra.busCompany, travel_id
        )
        itinerary: list[Checkpoint] = []
        for cp in cast(list[TiSistemasCheckpoint], full_itinerary):
            post_origin_checkpoint = (
                post_origin_checkpoint or cp.localId == travel_extra.origin
            )
            if post_origin_checkpoint:
                itinerary.append(
                    Checkpoint(
                        name=f"{cp.localNome} - {cp.localUf}",
                        departure_at=datetime.strptime(
                            cp.dataHoraPartida, "%Y-%m-%d %H:%M:%S"
                        ),
                        distance_km=(
                            0
                            if cp.localId == travel_extra.origin
                            else round(cp.totalKm - last_total_km, 2)
                        ),
                    )
                )
                if cp.localId == travel_extra.destination:
                    break
                last_total_km = cp.totalKm
        return itinerary

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        departure_date,
        origin,
        destination: f"search:{self.ota_config.id}:{departure_date}:{origin}:{destination}:v3",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
        process_result=lambda result, self, *a, **kw: self._trace_process_result(
            result
        ),  # pyright: ignore
    )
    async def _search_travels(
        self,
        departure_date: date,
        origin: int,
        destination: int,
    ) -> tuple[list[TiSistemasTravel] | None, datetime | None, int]:
        try:
            travels = await self._circuit_breaker.call(
                self.retry_policy(self._session.travels_search),
                departure_date=departure_date,
                origin=origin,
                destination=destination,
            )
            last_synced = datetime.now(timezone.utc)
            cache_ttl = await self._dynamic_search_ttl(
                travels,
                origin,
                destination,
                departure_date,
            )
            return travels, last_synced, cache_ttl
        except (httpx.HTTPStatusError, CircuitOpenError) as error:
            raise OTASearcherException(
                f"Search exception for {self.__class__.__name__}"
            ) from error
        except httpx.TimeoutException as error:
            raise OTASearcherTimeoutException(
                f"Search timed out for {self.__class__.__name__}"
            ) from error

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self,
        company_id,
        travel_id: f"itinerary:{self.ota_config.id}:{company_id}:{travel_id}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _travel_itinerary(
        self, company_id: str, travel_id: str
    ) -> list[TiSistemasCheckpoint]:
        return await self.retry_policy(self._session.get_itinerary)(
            company_id=company_id, travel_id=travel_id
        )

    @memoize(
        cache,
        ttl=60,
        cache_key=lambda self,
        departure_date,
        origin,
        destination,
        company_id,
        id: f"map:{self.ota_config.id}:{departure_date.isoformat()}:{origin}:{destination}:{company_id}:{id}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _seating_map(
        self,
        departure_date: date,
        origin: int,
        destination: int,
        company_id: str,
        id: int,
    ) -> TravelInfo:
        return await self.retry_policy(self._session.get_bus_layout)(
            departure_date=departure_date,
            origin=origin,
            destination=destination,
            company_id=company_id,
            id=id,
        )
