import asyncio
from dataclasses import asdict, dataclass, replace
from datetime import date, datetime, timedelta, timezone
from datetime import time as dtime
from typing import cast, override

import dacite
import httpx
from tenacity import (
    retry,
    retry_if_not_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    Stopover,
    Travel,
)
from marketplace.otas.exception import (
    OTASearcherException,
    OTASearcherInvalidParameters,
    OTASearcherTimeoutException,
)
from marketplace.otas.praxio.client import (
    ListaPartidasTFOResponse,
    LoginResponse,
    Partida,
    PartidasResponse,
    Poltrona,
    PraxioClient,
    PraxioClientError,
)
from marketplace.searcher import OTASearcher

PROVIDER_NAME = "praxio"


@dataclass
class PraxioTravelExtra:
    IdViagem: int
    TipoVeiculo: int
    CodigoOrigem: int
    CodigoDestino: int
    Andares: int
    TipoHorario: str | None
    TipoServico: int | None
    classe_final: str | None


def _login_session_ttl(response: LoginResponse, *args, **kwargs):
    # TempoSessao é em minutos
    return response.TempoSessao * 60 - 10


class PraxioSearcher(OTASearcher):
    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._session = PraxioClient(base_url=base_url, log_prefix=self.ota_config.name)

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_not_exception_type(dacite.WrongTypeError),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        search_response, last_synced, _ = await self._search_travels(
            departure_date=departure_date,
            origem_id=origin.extra["IdLocalidade"],
            destino_id=destination.extra["IdLocalidade"],
        )
        search_response = cast(PartidasResponse, search_response)

        if not search_response or not search_response.ListaPartidas:
            return []

        found = []
        for partida in search_response.ListaPartidas:
            seat_type = self._get_ota_seat_type_by_name(partida.classe_final)
            company = self._get_ota_company(partida.ViagemTFO.IdEstabelecimentoLinha)
            if seat_type is None or company is None:
                continue

            travel = Travel(
                ota=PROVIDER_NAME,
                ota_config_id=self.ota_config.id,
                code=str(partida.IdViagem),
                service_code=str(partida.IdViagem),
                company=company,
                itinerary=[],
                origin=cast(Place, origin.place),
                destination=cast(Place, destination.place),
                # TODO: handle timezone
                departure_at=datetime.fromisoformat(partida.ViagemTFO.DataHoraInicio),
                arrival_at=datetime.fromisoformat(partida.dta_hora_chegada),
                seat_type=seat_type,
                price=partida.preco_final,
                available_seats=partida.ViagemTFO.PoltronasDisponiveis,
                total_seats=partida.ViagemTFO.QtdPoltronas,
                last_synced=last_synced,
                extra=asdict(
                    PraxioTravelExtra(
                        IdViagem=partida.IdViagem,
                        TipoVeiculo=partida.TipoVeiculo,
                        CodigoOrigem=partida.ViagemTFO.CodigoOrigem,
                        CodigoDestino=partida.ViagemTFO.CodigoDestino,
                        Andares=partida.Andares,
                        TipoHorario=partida.ViagemTFO.TipoHorario,
                        TipoServico=partida.ViagemTFO.TipoServico,
                        classe_final=partida.classe_final,
                    )
                ),
            )
            if partida.conexoes:
                if partida.TipoConexao != 1:  # 0: conexão; 1: escala
                    # TODO implements connections
                    continue
                stopovers = self._build_stopovers(partida)
                if not stopovers:
                    continue
                travel.stopovers = stopovers

            found.append(travel)
        return found

    def _build_stopovers(self, partida: Partida) -> list[Stopover] | None:
        assert partida.conexoes

        stopovers = []

        # Primeira perna
        stopovers.append(
            Stopover(
                origin_ota_place_id=partida.ViagemTFO.CodigoOrigem,
                destination_ota_place_id=partida.ViagemTFO.CodigoDestino,
                extra=asdict(
                    PraxioTravelExtra(
                        IdViagem=partida.IdViagem,
                        TipoVeiculo=partida.TipoVeiculo,
                        CodigoOrigem=partida.ViagemTFO.CodigoOrigem,
                        CodigoDestino=partida.ViagemTFO.CodigoDestino,
                        Andares=partida.Andares,
                        TipoHorario=partida.ViagemTFO.TipoHorario,
                        TipoServico=partida.ViagemTFO.TipoServico,
                        classe_final=partida.classe_final,
                    )
                ),
            )
        )

        # Demais conexões
        for conexao in partida.conexoes:
            seat_type = self._get_ota_seat_type_by_name(conexao.classe_final)
            company = self._get_ota_company(conexao.viagemTFO.IdEstabelecimentoLinha)

            if not seat_type or not company:
                return None

            stopovers.append(
                Stopover(
                    origin_ota_place_id=conexao.viagemTFO.CodigoOrigem,
                    destination_ota_place_id=conexao.viagemTFO.CodigoDestino,
                    extra=asdict(
                        PraxioTravelExtra(
                            IdViagem=conexao.IdViagem,
                            TipoVeiculo=conexao.IdTipoVeiculo,
                            CodigoOrigem=conexao.viagemTFO.CodigoOrigem,
                            CodigoDestino=conexao.viagemTFO.CodigoDestino,
                            Andares=conexao.Andares,
                            TipoHorario=conexao.viagemTFO.TipoHorario,
                            TipoServico=None,
                            classe_final=conexao.classe_final,
                        )
                    ),
                )
            )

        return stopovers

    def _has_few_seats_travel(self, search_response: list[Partida]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for partida in search_response:
            if partida.ViagemTFO.PoltronasDisponiveis < threshold:
                return True

        return False

    @override
    @memoize(cache, ttl=3600, cache_key=lambda self: f"places:{self.ota_config.id}")
    async def list_places(self) -> list[OTAPlace]:
        places = set()
        for localidade in await self.retry_policy(self._session.partidas_origens)(
            id_sessao_op=await self._id_sessao_op()
        ):
            places.add(
                OTAPlace(
                    ota_config_id=self.ota_config.id,
                    name=localidade.Descricao,
                    extra={"IdLocalidade": localidade.IdLocalidade},
                )
            )
            for localidade in await self.retry_policy(self._session.partidas_destinos)(
                id_sessao_op=await self._id_sessao_op(),
                id_origem=localidade.IdLocalidade,
            ):
                places.add(
                    OTAPlace(
                        ota_config_id=self.ota_config.id,
                        name=localidade.Descricao,
                        extra={"IdLocalidade": int(localidade.IdLocalidade)},
                    )
                )

        return list(places)

    @override
    async def available_seats(self, travel: InputTravel) -> list[Seat]:
        response = await self._retorna_poltronas(
            id_viagem=travel.extra["IdViagem"],
            id_tipo_veiculo=travel.extra["TipoVeiculo"],
            id_loc_origem=travel.extra["CodigoOrigem"],
            id_loc_destino=travel.extra["CodigoDestino"],
            andares=travel.extra["Andares"],
        )

        seats = []
        for andar, poltronas in response:
            for poltrona in poltronas:
                category = travel.extra["classe_final"]
                seat_type = self._get_ota_seat_type_by_name(category)
                seats.append(
                    Seat(
                        number=poltrona.Caption or f"{poltrona.NumeroPoltrona:02d}",
                        available=poltrona.Situacao == 0,
                        category=category,
                        seat_type=seat_type.seat_type if seat_type else None,
                        floor=andar,
                        row=((poltrona.NumeroPoltrona - 1) // 4) + 1,
                        column=[1, 2, 4, 3][(poltrona.NumeroPoltrona - 1) % 4],
                        extra={
                            "NumeroPoltrona": poltrona.NumeroPoltrona,
                        },
                        block_key=None,
                    )
                )
        return sorted(seats, key=lambda s: (s.row, s.column))

    @override
    async def block_seat(self, travel: Travel, seat: Seat) -> Seat:
        response = await self._session.verifica_poltrona(
            id_sessao_op=await self._id_sessao_op(),
            id_viagem=travel.extra["IdViagem"],
            id_tipo_veiculo=travel.extra["TipoVeiculo"],
            id_loc_origem=travel.extra["CodigoOrigem"],
            id_loc_destino=travel.extra["CodigoDestino"],
            andar=seat.floor,
            id_poltrona=seat.extra["NumeroPoltrona"],
            bloqueia=1,
        )
        return replace(seat, block_key="t" if response.Marcada else None)

    @override
    async def unblock_seat(self, travel: Travel, seat: Seat) -> bool:
        response = await self._session.desmarca_poltrona(
            id_sessao_op=await self._id_sessao_op(),
            id_viagem=travel.extra["IdViagem"],
            id_tipo_veiculo=travel.extra["TipoVeiculo"],
            id_loc_origem=travel.extra["CodigoOrigem"],
            id_loc_destino=travel.extra["CodigoDestino"],
            andar=seat.floor,
            id_poltrona=seat.extra["NumeroPoltrona"],
        )
        return response.Marcada

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        travel_extra = PraxioTravelExtra(**travel.extra)
        itinerary: list[Checkpoint] = []
        ota_itinerary: ListaPartidasTFOResponse = await self._lista_viagens_tfo(
            travel_extra.IdViagem
        )
        post_origin_checkpoint = False
        for partida in ota_itinerary.ListaPartidasTFO:
            post_origin_checkpoint = (
                post_origin_checkpoint
                or partida.Localidade.IDLocalidade == travel_extra.CodigoOrigem
            )
            if post_origin_checkpoint:
                itinerary.append(
                    Checkpoint(
                        name=partida.Localidade.Descricao,
                        departure_at=datetime.fromisoformat(partida.DataPartida),
                    )
                )
            if partida.Localidade.IDLocalidade == travel_extra.CodigoDestino:
                break
        return itinerary

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        departure_date,
        origem_id,
        destino_id: f"search:{self.ota_config.id}:{departure_date}:{origem_id}:{destino_id}:v2",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
        process_result=lambda result, self, *a, **kw: self._trace_process_result(
            result
        ),  # pyright: ignore
    )
    async def _search_travels(
        self, departure_date: date, origem_id: int, destino_id: int, andares: int = 1
    ) -> tuple[PartidasResponse | None, datetime | None, int]:
        try:
            partidas_response = await self._circuit_breaker.call(
                self.retry_policy(self._session.partidas),
                id_sessao_op=await self._id_sessao_op(),
                id_estabelecimento=await self._id_estabelecimento_venda(),
                data_partida=datetime.combine(departure_date, dtime.min),
                localidade_origem=origem_id,
                localidade_destino=destino_id,
            )
            last_synced = datetime.now(timezone.utc)
            cache_ttl = await self._dynamic_search_ttl(
                partidas_response.ListaPartidas if partidas_response else None,
                origem_id,
                destino_id,
                departure_date,
            )
            return partidas_response, last_synced, cache_ttl
        except PraxioClientError as error:
            if error.message == "Data da viagem menor que a data atual.":
                return (
                    None,
                    datetime.now(timezone.utc),
                    self.ota_config.search_cache.ttl_not_available,
                )
            if error.message == "O local de origem e de destino não pode ser o mesmo.":
                raise OTASearcherInvalidParameters(
                    f"Invalid search parameters for {self.__class__.__name__}: {error.message}"
                ) from error
            raise OTASearcherException(
                f"Search exception for {self.__class__.__name__}"
            ) from error
        except (httpx.HTTPStatusError, CircuitOpenError) as error:
            raise OTASearcherException(
                f"Search exception for {self.__class__.__name__}"
            ) from error
        except httpx.TimeoutException as error:
            raise OTASearcherTimeoutException(
                f"Search timed out for {self.__class__.__name__}"
            ) from error

    async def _retorna_poltronas(
        self,
        id_viagem: int,
        id_tipo_veiculo: int,
        id_loc_origem: int,
        id_loc_destino: int,
        andares: int,
    ) -> list[tuple[int, list[Poltrona]]]:
        responses = await asyncio.gather(
            *[
                self.retry_policy(self._session.retorna_poltronas)(
                    id_sessao_op=await self._id_sessao_op(),
                    id_viagem=id_viagem,
                    id_tipo_veiculo=id_tipo_veiculo,
                    id_loc_origem=id_loc_origem,
                    id_loc_destino=id_loc_destino,
                    andar=andar,
                )
                for andar in range(1, andares + 1)
            ]
        )
        poltronas = []
        for andar, response in zip(range(1, andares + 1), responses):
            poltronas.append((andar, response.LaypoltronaXml.PoltronaXmlRetorno))
        return poltronas

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self, id_viagem: f"itinerary:{self.ota_config.id}:{id_viagem}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _lista_viagens_tfo(self, id_viagem: int) -> ListaPartidasTFOResponse:
        return await self.retry_policy(self._session.lista_partidas_tfo)(
            id_sessao_op=await self._id_sessao_op(), id_viagem=id_viagem
        )

    @memoize(
        cache,
        ttl=_login_session_ttl,
        cache_key=lambda self: f"login:{self.ota_config.id}",
        lock=lock,
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _login(self) -> LoginResponse:
        try:
            response = await self.retry_policy(self._session.efetua_login)(
                nome=cast(str, self.ota_config.config["nome"]),
                senha=cast(str, self.ota_config.config["senha"]),
                cliente=cast(str, self.ota_config.config["cliente"]),
                empresa=cast(str, self.ota_config.config["empresa"]),
            )
            return response
        except:
            print("DEU RUIMMMM", self.ota_config)
            raise

    async def _id_sessao_op(self) -> str:
        response = await self._login()
        return response.IdSessaoOp

    async def _id_estabelecimento_venda(self) -> int:
        # id estabelecimento que representa nosso login dentro do sistema Praxio
        # diferente do id_estabelecimento_linha
        response = await self._login()
        return response.EstabelecimentoXml.IDEstabelecimento
