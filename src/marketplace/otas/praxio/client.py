import time
from dataclasses import dataclass, field
from datetime import datetime
from http import HTTPStatus
from json import JSONDecodeError
from typing import Any, List, Optional

import dacite
import httpx

from marketplace.otas import http
from marketplace.otas.exception import OTANotJSONResponse

MAP_SEAT_TYPES = {
    0: "Convencional com sanitário",
    2: "Convencional sem sanitário",
    3: "Semileito",
    4: "Leito com ar condicionado",
    5: "Leito sem ar condicionado",
    6: "Executivo",
    7: "Semiurbano",
}


@dataclass
class CommonAttributesMixin:
    Strings: List[Any]
    Integers: List[Any]
    Floats: List[Any]
    VarStr: Optional[str]
    VarInt: int
    VarFloat: float
    Sucesso: bool
    Advertencia: bool


@dataclass
class Estabelecimento:
    Id: int
    NomeFantasia: str
    RazaoSocial: str
    Cnpj: str
    Endereco: str
    Numero: str
    Complemento: str
    Bairro: str
    Cidade: str
    Uf: str
    Cep: str
    Telefone: Optional[str]


@dataclass
class Localidade:
    IdLocalidade: str
    Descricao: str
    IdCidade: str


@dataclass
class Gerenciamento:
    Descricao: Optional[str] = None
    ArredondaTarifa: Optional[int] = None
    IdadeMaxColo: Optional[int] = None
    IdTipo: Optional[int] = None
    # Optional URLs and fields that might not always be present
    IdGerenciamento: Optional[int] = None
    IdCliente: Optional[int] = None
    ImprimeMonitriip: Optional[int] = None
    ORGAO_INTEREST: Optional[int] = None
    EmpresaConveniada: Optional[int] = None
    Comissao: Optional[float] = None
    CodigoBilhetagem: Optional[str] = None
    SenhaBilhetagem: Optional[str] = None
    UrlServidorWS: Optional[str] = None
    CodigoEmpresa: Optional[str] = None
    UrlAgr: Optional[str] = None
    UsuarioAgr: Optional[str] = None
    SenhaAgr: Optional[str] = None
    UrlConfAgr: Optional[str] = None
    UrlConsAgr: Optional[str] = None
    UrlCancAgr: Optional[str] = None
    RESERVADEFIC_GEREN: int = 0
    # Other similar fields omitted for brevity


@dataclass
class ViagemTFO:
    IdEstabelecimentoLinha: int
    ControlaPoltornaTrecho: int
    NomeLinha: str
    CodigoOrigem: int
    DescricaoOrigem: str
    CodigoDestino: int
    DescricaoDestino: str
    # Datetimes are represented as strings for simplicity, could use datetime objects
    DataHoraInicio: str
    DataHoraEmbarque: str
    TempoViagem: str
    StrDistanciaViagem: str
    DistanciaViagem: int
    DtaHoraChegada: str
    ValorTarifa: str
    TipoHorario: str
    UFOrigem: str
    UFDestino: str
    # Many optional fields
    DescricaoDestinoConexao: Optional[str] = None
    OfertaAPP5: int = 0
    OfertaAP: int = 0
    QtdPoltronas: int = 0
    PoltronasDisponiveis: int = 0
    IdLinha: str = ""
    IdVeiculo: str = ""
    Motorista: str = ""
    Empresa: Optional[str] = ""
    Filial: Optional[str] = ""
    HoraPartida: str = ""
    HoraTolerancia: str = ""
    TipoLinha: int = 1
    ObsLinha: str = ""
    ObsLinhaAux: str = ""
    GratuidadeDisponivel: int = 0
    ListaGratuidades: Optional[Any] = None
    Plataforma: Optional[Any] = None
    DuracaoViagem: str = ""
    TipoServico: Optional[int] = None


@dataclass
class Partida:
    ViagemTFO: ViagemTFO
    IdViagem: int
    IdViagemComp: int
    TipoVeiculo: int
    IdLocalidade: int
    Gerenciamento: Gerenciamento
    conexoes: Optional[list["Conexao"]]
    TotalConexao: float
    DescontoConexao: float
    DescConexao: Optional[str]
    PagamentosNaoPermitidos: List[Any]
    IdRota: int
    TipoConexao: int
    DescTipoConexao: Optional[str]
    IdDesconto: int
    Pricing: float
    VlTarifaAnterior: float
    ValorMaiorDesconto: float
    Tarifa: float
    CobrarTxEmbarque: int
    DadosViagem: str
    EstabelecimentoEmissor: Optional[Any]
    TxEmbarque: float
    Seguro: float
    Pedagio: float
    Desconto: float
    VlTarifa: float
    Andares: int
    ControlaCliente: int
    ControlaPoltrona: int
    CPOL: int
    IdCodigoLinha: int
    CodigoLinha: str
    Extra: int
    Servico: int
    NaoVerifIdoso50: int
    NaoVerifIdoso100: int
    NaoVerifDeficiente: int
    Comentario: Optional[str]
    CodigoBilhetagem: str
    LayoutBarco: int
    BloqDescontoManual: int
    BloqAcrescimoManual: int
    PermissaoDescManualEmpresa: int
    TxConveniencia: float
    SemPoltronaWeb: int
    LinhaAlias: Optional[int]
    Outros: float
    PedagioVolta: Optional[float]
    ChavePricing: Optional[str] = None
    TxEmbCortesia: Optional[float] = None
    NaoPermitirReserva: Optional[bool] = None
    DescPricing: Optional[str] = None

    @property
    def preco_final(self):
        if self.conexoes and len(self.conexoes) > 0:
            raw_price = self.TotalConexao
            discount = 0
            if self.ValorMaiorDesconto:
                discount = self.ValorMaiorDesconto
            return round(raw_price - discount, 2)
        preco = (
            self.TxEmbarque
            + self.Seguro
            + self.Pedagio
            + self.VlTarifa
            - self.Desconto
            - self.Pricing
        )
        return round(preco, 2)

    @property
    def classe_final(self):
        if self.ViagemTFO.TipoHorario:
            return self.ViagemTFO.TipoHorario
        if self.ViagemTFO.TipoServico:
            return MAP_SEAT_TYPES[self.ViagemTFO.TipoServico]

        # campos de classe são opcionais na Praxio.
        # usa a primeira opção do map caso estejam vazios (comportamento sugerido pela própria Praxio)
        return MAP_SEAT_TYPES[0]

    @property
    def dta_hora_chegada(self):
        if self.conexoes:
            return self.conexoes[-1].viagemTFO.DtaHoraChegada
        return self.ViagemTFO.DtaHoraChegada


@dataclass
class PartidasResponse(CommonAttributesMixin):
    ParametrosEntrada: dict = field(default_factory=dict)
    ListaPartidas: List[Partida] = field(default_factory=list)
    ListaMultiempresas: List[Any] = field(default_factory=list)
    ListaLogos: List[Any] = field(default_factory=list)
    Xml: Optional[Any] = None
    IdErro: Optional[Any] = None
    Mensagem: Optional[str] = None
    MensagemDetalhada: Optional[str] = None


@dataclass
class EstabelecimentoXml:
    AtualizaSegurancaCertificado: Optional[str]
    IDEstabelecimento: int
    RazaoSocial: str
    NomeFantasia: str
    Cnpj: str
    Endereco: str
    Uf: str
    IMunicipal: str
    IEstadual: str
    Telefone: Optional[str]
    IdEmpresa: int
    Numero: str
    Complemento: str
    Bairro: str
    IdCidade: int
    NomeCidade: str
    Cep: str
    Cnae: Optional[str]
    Certificado: Optional[str]
    IdMoeda: int
    AmbienteProducao: bool
    TARBPe: Optional[str]
    SSLType: Optional[str]
    SSLLib: Optional[str]
    SSLHttpLib: Optional[str]
    SSLCryptLib: Optional[str]
    SSLXmlSignLib: Optional[str]
    SerieBpe: int
    Fax: Optional[str]
    Contato: Optional[str]
    Obs: Optional[str]
    CupomEmbarqueVoucher: bool
    NaoEmbarqueAutomatico: bool
    NaoEmbarqueAutomaticoAbertura: bool
    UtilizarBpe: Optional[str]
    FormatoLogo: Optional[str]
    Logo: Optional[str]
    CodigoExterno2: Optional[str]
    CodigoExterno: Optional[str]
    InscricaoJuntac: Optional[str]
    Agenciarod: Optional[str]
    NumeracaoMatriz: Optional[str]
    DadosMatriz: Optional[str]
    EstabelecimentoEmissor: Optional[str]
    NumeroN: Optional[str]
    FormaPgto: List[str]
    AmbienteBpe: Optional[str]
    AgenciaBloqueada: int
    MensagemAlerta: str
    EstornarComissaoDevolucao: float
    TaxaEmbarqueRepasse: bool
    ComputarTaxaPorDataViagem: bool
    ComputarTxRepasseVoucher: bool
    LocalidadeRepasseTaxaEmbarque: int
    DescLocalidadeRepasse: Optional[str]
    ListarEstabSemGrupoAgencia: bool
    IdGrupoAgencia: Optional[str]
    NaoCobrarTaxaEmbarque: bool
    CnpjTef: Optional[str]
    IpServidorTef: Optional[str]
    LojaTef: Optional[str]
    TerminalTef: Optional[str]
    PortaTef: Optional[str]
    MsgPinpadTef: Optional[str]
    EmpresaTef: int
    ImprimirTxEmbW2iSemDll: bool
    BloqueioPartidas: bool
    PartidasBloqueio: bool
    GeraQrcodePix: int
    TipoChavePix: int
    ChavePix: Optional[str]
    ECommerce: int
    OcultarAgenciaRelatorio: bool
    PagamentoPixTef: bool
    VendaManualCartao: bool
    VendaManualPix: bool
    UtilizaSolAutReimp: Optional[str]
    UtilizaSolAutReimpExcesso: Optional[str]
    PassagemWhatsapp: bool
    CelularWhatsapp: Optional[str]
    TokenPlugSocial: Optional[str]
    CadastraTerminalTef: int
    ListaTerminalTef: Optional[str]
    ImpressaoPadrao: int
    AbrirCaixaAuto: int
    RetornoSimples: bool
    OrigemPadrao: int
    SerieNaoFiscal: str
    DadosEmissaoBPe: Optional[str]
    BlocoEletronico: int
    QtdCupomEmbarqueNFiscal: Optional[int]
    ControlarSaldoFechamento: int
    DadosControlarSaldoFechamento: Optional[str]
    ExibirStatusImpressora: Optional[str]
    TaxaConveniencia: Optional[str]
    PercTaxaConveniencia: Optional[str]
    MensagemTaxaConveniencia: Optional[str]
    AliquotaIssTaxaConveniencia: Optional[str]
    BlocoBarco: int
    TokenFidelidade: Optional[str]
    NaoAplicarTxEmbarque: bool
    Latitude: str
    Longitude: str
    ChavePixBB: Optional[str]
    DevAppKeyPixBB: Optional[str]
    ClientIdPixBB: Optional[str]
    ClientSecretPixBB: Optional[str]
    BasicTokenPixBB: Optional[str]
    UrlLoginPixBB: Optional[str]
    UrlApiPixBB: Optional[str]
    ConsiderarApenasBPe: bool
    ReplicarCertificadoGrupoAgencia: bool
    ComissaoDataAcerto: bool
    DataComissao: Optional[str]
    NaoEmbApenasSolAut: bool
    ImprimeBlocoEletronicoPdf: bool
    ApuracaoComissaoConsulta: bool
    PdvFidelidade: bool


@dataclass
class GaragemXml:
    IDGaragem: int
    Descricao: str
    Situacao: int
    CodigoExterno: int


@dataclass
class EmpresaXml:
    NomeFantasia: str
    RazaoSocial: str
    Email: Optional[str]
    Uf: str
    Cnpj: str
    IMunicipal: str
    IEstadual: str
    Endereco: str
    Numero: str
    Complemento: str
    Bairro: str
    IdCidade: int
    NomeCidade: str
    Cep: str
    Telefone: Optional[str]
    TokenFidelidade: str
    CodigoIndicacaoFidelidade: str
    Numero1: str
    PoliticaPrivacidade: str


@dataclass
class LoginResponse(CommonAttributesMixin):
    ParametrosEntrada: dict
    Comissao: float
    RelatorioAgencia: int
    Telefone: Optional[str]
    Email: Optional[str]
    MaxDescManual: float
    MaxAcresManual: float
    PermDescManualOperador: bool
    PermAcresManualOperador: bool
    DescontoPercentual: float
    AcrescimoPercentual: float
    IdOperador: int
    IdSessaoOp: str
    Nome: str
    Super: int
    TempoInativarOperadorAposTempo: int
    TempoSessao: int  # é em minutos
    VisualizouLGPD: int | None
    IdJuncao: int
    RelSuperOp: bool
    LiberarCancTEF: bool
    LiberarReimpPass: bool
    LiberarMarcarVoucher: bool
    InativarOperadorAposTempo: bool
    VisualizouTermoLGPD: Optional[int]
    VendDevOutroCaixaSemCaixa: bool
    UsuarioConsulta: bool
    ApenasBiometria: Optional[str]
    AlterarSenhaOperadorXDias: bool
    TempoAlterarSenhaOperadorXDias: int
    PassouQtdDiasAlterarSenha: bool
    ObrigaCadastroPos: Optional[str]
    EstabelecimentoPadrao: int
    EstabelecimentoXml: EstabelecimentoXml
    GaragemXml: GaragemXml
    EmpresaXml: EmpresaXml
    IdPerfilOperador: int
    Xml: Optional[str]
    IdErro: str
    Mensagem: str
    MensagemDetalhada: str


@dataclass
class Poltrona:
    ParametrosEntrada: dict
    QtdDisponivel: int
    QtdGratuidade: int
    NumeroPoltrona: int
    Situacao: int
    Caption: Optional[str]
    IntCaption: int
    Marcada: bool
    IdReserva: Optional[int | str]
    ReservaSite: bool
    Sucesso: bool
    Advertencia: bool


@dataclass
class LaypoltronaXml:
    PoltronaXmlRetorno: List[Poltrona]


@dataclass
class RetornaPoltronasResponse:
    ParametrosEntrada: dict
    QtdDisponivel: int
    QtdGratuidade: int
    NumeroPoltrona: int
    Situacao: int
    Caption: Optional[str]
    IntCaption: int
    Marcada: bool
    IdReserva: Optional[str]
    LaypoltronaXml: LaypoltronaXml


@dataclass
class VerificaPoltronaResponse(CommonAttributesMixin):
    Marcada: bool
    IdErro: str
    Mensagem: Optional[str]


@dataclass
class ViagemTFOConexao:
    IdEstabelecimentoLinha: int
    ControlaPoltornaTrecho: int
    NomeLinha: str
    CodigoOrigem: int
    DescricaoOrigem: str
    CodigoDestino: int
    DescricaoDestino: str
    DescricaoDestinoConexao: Optional[str]
    DataHoraInicio: str
    DataHoraEmbarque: str
    TempoViagem: str
    StrDistanciaViagem: str
    DistanciaViagem: int
    DtaHoraChegada: str
    ValorTarifa: str
    OfertaAPP5: int
    OfertaAP: int
    QtdPoltronas: int
    TipoHorario: str
    PercentualImposto: str
    ValorImposto: str
    PoltronasDisponiveis: int
    IdLinha: str
    IdVeiculo: Optional[str]
    Motorista: Optional[str]
    Empresa: Optional[str]
    Filial: Optional[str]
    HoraPartida: str
    HoraTolerancia: str
    TipoLinha: int
    UFDestino: str
    ObsLinha: str
    ObsLinhaAux: str
    GratuidadeDisponivel: int
    ListaGratuidades: Optional[str]
    Plataforma: Optional[str]
    UFOrigem: str
    TipoServico: Optional[str]
    DuracaoViagem: str


@dataclass
class Conexao:
    IdViagem: int
    IdViagemComp: int
    IdTipoVeiculo: int
    tarifa: float
    Andares: int
    ControlaCliente: int
    TxEmbarque: float
    Seguro: float
    Pedagio: float
    Desconto: float
    Pricing: float
    VlTarifa: float
    viagemTFO: ViagemTFOConexao
    ControlaPoltrona: int
    cPOL: int
    idCodigoLinha: int
    Extra: int
    Servico: int
    CodigoLinha: str
    Gerenciamento: Gerenciamento
    CodigoBilhetagem: str
    DescConexao: str
    DataViagem: str
    HoraViagem: str
    TipoViagem: int
    QtdLugares: int
    CodigoOrigem: int
    CodigoDestino: int
    Sentido: int
    IdLocalidade: int
    DadosViagem: str
    IdRota: int
    LayoutBarco: int
    CobraTaxa: bool
    CobraPedagio: bool
    CobraSeguro: bool
    CobraOutros: bool
    TxConveniencia: float
    Outros: float

    @property
    def classe_final(self):
        if self.viagemTFO.TipoHorario:
            return self.viagemTFO.TipoHorario
        if self.viagemTFO.TipoServico:
            return MAP_SEAT_TYPES[int(self.viagemTFO.TipoServico)]

        # campos de classe são opcionais na Praxio.
        # usa a primeira opção do map caso estejam vazios (comportamento sugerido pela própria Praxio)
        return MAP_SEAT_TYPES[0]


@dataclass
class LocalidadePartida:
    ListEstabelecimentos: Optional[object]
    BilheteEmbW2i: int
    IDLocalidade: int
    Descricao: str
    Sigla: str
    IdRegiao: Optional[int]
    Uf: str
    IdEstabelecimento: int
    IdCidade: int
    TxEmbIdoso50: int
    TxEmbIdoso100: int
    TxEmbPasseLivre: int
    PedagioIdoso100: int
    PedagioIdoso50: int
    Codigo: int
    AgenciasCargas: Optional[object]
    LastUpdate: Optional[str]
    TxPedagioPasseLivre: Optional[float]
    CodigoSgltar: int
    TxEmb50Idoso50: int
    TxEmb50Def50: int
    TxEmb50Estud50: int
    TxEmb50Jov50: int
    TxEmbIdosoDef: int
    CodigoSigma: Optional[str]
    TxEmbCortesia: int
    TxEmbJovem50: int
    TxEmbJovem100: int
    TxEmbAcompanhantePL: int


@dataclass
class PartidaTFO:
    DataBloqSemPassageiro: str
    HoraBloqSemPassageiro: Optional[str]
    IDViagem: int
    DataPartida: str
    HoraPartida: str
    Sentido: int
    Plataforma: str
    HoraChegada: Optional[str]
    ControlaPoltrona: int
    ControlaPassageiros: int
    ControlaCliente: int
    Localidade: LocalidadePartida
    DataChegada: str
    Obs: Optional[str]
    HoraTolerancia: Optional[str]
    DataExibirViagem: str
    HoraExibirViagem: Optional[str]
    QtdPoltronaBloqueio: int
    Comentario: str


@dataclass
class ListaPartidasTFOResponse:
    ParametrosEntrada: dict
    ListaTrechosTFO: Optional[List[object]]
    ListaPartidasTFO: List[PartidaTFO]


class AsyncClient(httpx.AsyncClient):
    def __init__(self, log_prefix: str = "HTTPClient", *args, **kwargs):
        self.log_prefix = log_prefix
        super().__init__(*args, **kwargs)

    async def _send_single_request(self, request: httpx.Request) -> httpx.Response:
        start = time.monotonic()

        try:
            response = await super()._send_single_request(request)
            await response.aread()

            payload = response.json()
            if response.status_code == HTTPStatus.OK and payload.get("IdErro"):
                raise PraxioClientError(
                    payload["IdErro"],
                    payload.get("Mensagem") or payload["IdErro"],
                    response,
                )

            http.log_http_request_response(
                request, response, time.monotonic() - start, self.log_prefix
            )
            return response
        except JSONDecodeError as exc:
            http.log_http_request_error(
                request, exc, time.monotonic() - start, self.log_prefix
            )
            raise OTANotJSONResponse("NotJSONResponse") from exc
        except Exception as exc:
            http.log_http_request_error(
                request, exc, time.monotonic() - start, self.log_prefix
            )
            raise


class PraxioClient:
    def __init__(
        self, base_url: str, timeout: int = 20, log_prefix: Optional[str] = None
    ):
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            log_prefix=f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__,
        )

    async def efetua_login(
        self,
        nome: str,
        senha: str,
        cliente: str,
        empresa: str,
        sistema: str = "WINVR.EXE",
        tipo_db: int = 1,
        tipo_aplicacao: int = 0,
    ):
        response = await self._client.post(
            "/Autumn/Login/EfetuaLogin",
            json={
                "Nome": nome,
                "Senha": senha,
                "Sistema": sistema,
                "Empresa": empresa,
                "TipoDB": tipo_db,
                "Cliente": cliente,
                "TipoAplicacao": tipo_aplicacao,
            },
        )
        response.raise_for_status()
        return dacite.from_dict(LoginResponse, response.json())

    async def partidas_origens(self, id_sessao_op: str) -> list[Localidade]:
        response = await self._client.post(
            "/Autumn/Partidas/Origens",
            json={
                "IdSessaoOp": id_sessao_op,
            },
        )
        response.raise_for_status()
        return [
            dacite.from_dict(Localidade, item)
            for item in response.json()["Xml"]["NewDataSet"]["Table"]
        ]

    async def partidas_destinos(
        self,
        id_sessao_op: str,
        id_origem: int | str,
    ) -> list[Localidade]:
        response = await self._client.post(
            "/Autumn/Partidas/Origens",
            json={
                "IdSessaoOp": id_sessao_op,
                "IdOrigem": id_origem,
            },
        )
        response.raise_for_status()
        return [
            dacite.from_dict(Localidade, item)
            for item in response.json()["Xml"]["NewDataSet"]["Table"]
        ]

    async def partidas(
        self,
        id_sessao_op: str,
        id_estabelecimento: int,
        localidade_origem: int,
        localidade_destino: int,
        data_partida: datetime,
    ) -> PartidasResponse:
        response = await self._client.post(
            "/Autumn/Partidas/Partidas",
            json={
                "IdSessaoOp": id_sessao_op,
                "LocalidadeOrigem": int(localidade_origem),
                "LocalidadeDestino": int(localidade_destino),
                "DataPartida": data_partida.isoformat(),
                "sugestaoPassagem": 1,  # Mostra quantidade de poltronas disponveis pra compra
                "DescontoAutomatico": 1,  # Retorna o valor do desconto na linha caso tenha
                "IdEstabelecimentoVenda": id_estabelecimento,  #  Retornar desconto específico por agencia de vendas
            },
        )
        response.raise_for_status()
        return dacite.from_dict(PartidasResponse, response.json())

    async def retorna_poltronas(
        self,
        id_sessao_op: str,
        id_viagem: int,
        id_tipo_veiculo: int,
        id_loc_origem: int,
        id_loc_destino: int,
        andar: int = 1,
    ) -> RetornaPoltronasResponse:
        response = await self._client.post(
            "/Autumn/Poltrona/retornaPoltronas",
            json={
                "IdSessaoOp": id_sessao_op,
                "IdViagem": id_viagem,
                "IdTipoVeiculo": id_tipo_veiculo,
                "IdLocOrigem": id_loc_origem,
                "IdLocDestino": id_loc_destino,
                "Andar": andar,
            },
        )
        response.raise_for_status()
        return dacite.from_dict(RetornaPoltronasResponse, response.json())

    async def lista_partidas_tfo(
        self, id_sessao_op: str, id_viagem: int
    ) -> ListaPartidasTFOResponse:
        response = await self._client.post(
            "/Autumn/Partidas/listaPartidasTFO",
            json={"IdSessaoOp": id_sessao_op, "IdViagem": id_viagem},
        )
        return dacite.from_dict(ListaPartidasTFOResponse, response.json())

    async def verifica_poltrona(
        self,
        id_sessao_op: str,
        id_viagem: int,
        id_tipo_veiculo: int,
        id_loc_origem: int,
        id_loc_destino: int,
        andar: int,
        id_poltrona: int,
        bloqueia: int,
    ) -> VerificaPoltronaResponse:
        response = await self._client.post(
            "/Autumn/Poltrona/VerificaPoltrona",
            json={
                "IdSessaoOp": id_sessao_op,
                "IdViagem": id_viagem,
                "IdTipoVeiculo": id_tipo_veiculo,
                "IdLocOrigem": id_loc_origem,
                "IdLocDestino": id_loc_destino,
                "Andar": andar,
                "IdPoltrona": id_poltrona,
                "Bloqueia": bloqueia,
            },
        )
        response.raise_for_status()
        return dacite.from_dict(VerificaPoltronaResponse, response.json())

    async def desmarca_poltrona(
        self,
        id_sessao_op: str,
        id_viagem: int,
        id_tipo_veiculo: int,
        id_loc_origem: int,
        id_loc_destino: int,
        andar: int,
        id_poltrona: int,
    ) -> VerificaPoltronaResponse:
        response = await self._client.post(
            "/Autumn/Poltrona/DesmarcaPoltrona",
            json={
                "IdSessaoOp": id_sessao_op,
                "IdViagem": id_viagem,
                "IdTipoVeiculo": id_tipo_veiculo,
                "IdLocOrigem": id_loc_origem,
                "IdLocDestino": id_loc_destino,
                "Andar": andar,
                "IdPoltrona": id_poltrona,
            },
        )
        response.raise_for_status()
        return dacite.from_dict(VerificaPoltronaResponse, response.json())


class PraxioClientError(RuntimeError):
    def __init__(self, error_id, message, response):
        self.error_id = error_id
        self.message = message
        self.response = response
        super().__init__(message)
