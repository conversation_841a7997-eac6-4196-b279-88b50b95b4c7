from dataclasses import dataclass
from datetime import date, datetime
from typing import Optional

import dacite
import httpx

from marketplace.otas.http import AsyncClient


class OTASectionalNotOffered(Exception): ...


@dataclass
class Empresa:
    id: int
    nome: str
    cnpj: Optional[str]


@dataclass
class Local:
    id: int
    cidade: str
    sigla: str
    uf: str
    empresas: str


@dataclass
class OrigemDestinos:
    origem: Local
    destinos: list[Local]


@dataclass
class SearchResponse:
    origem: Local
    destino: Local
    data: str
    lsServicos: list["Servico"]


@dataclass
class Servico:
    identificadorViagem: str
    servico: str
    rutaId: int
    prefixoLinha: str
    marcaId: int
    grupo: str
    grupoOrigemId: int
    grupoDestinoId: int
    saida: str
    chegada: str
    dataCorrida: str
    dataSaida: str
    poltronasLivres: int
    poltronasTotal: int
    preco: float
    precoOriginal: float
    classe: str
    empresa: str
    empresaId: int
    mensagemServico: str
    vende: bool
    bpe: bool
    km: float
    modalidadeBpe: str
    bpeImpressaoPosterior: bool
    numeroOnibus: Optional[str]
    tarifa: float
    restricaoDinamicaBloqueioTrecho: bool
    indRutaInternacional: bool
    conexao: Optional["Conexao"] | None = None
    cnpj: Optional[str] | None = None


@dataclass
class Conexao:
    servicoConexao: str
    localidadeConexao: str
    localidadeConexaoId: int
    empresa: str
    empresaId: int
    rutaId: int
    marcaId: int
    dataCorridaConexao: str
    dataSaidaConexao: str
    primeiroTrechoPoltronasLivres: int
    primeiroTrechoPoltronasTotal: int
    primeiroTrechoClasse: str
    primeiroTrechoDataCorrida: str
    primeiroTrechoDataSaida: str
    primeiroTrechoDataChegada: str
    primeiroTrechoHoraSaida: str
    primeiroTrechoHoraChegada: str
    primeiroTrechoPreco: str
    primeiroTrechoPrecoOriginal: str
    primeiroTrechoServico: str
    primeiroTrechoLinha: int
    primeiroTrechoEmpresa: str
    primeiroTrechoEmpresaId: int
    primeiroTrechoMarca: int
    primeiroTrechoOrigem: int
    primeiroTrechoOrigemDescricao: str
    primeiroTrechoDestino: int
    primeiroTrechoDestinoDescricao: str
    primeiroTrechoVende: bool
    primeiroTrechoIsBpe: bool
    primeiroTrechoSequencia: int
    segundoTrechoPoltronasLivres: int
    segundoTrechoPoltronasTotal: int
    segundoTrechoClasse: str
    segundoTrechoDataCorrida: str
    segundoTrechoDataSaida: str
    segundoTrechoDataChegada: str
    segundoTrechoHoraSaida: str
    segundoTrechoHoraChegada: str
    segundoTrechoPreco: str
    segundoTrechoPrecoOriginal: str
    segundoTrechoServico: str
    segundoTrechoLinha: int
    segundoTrechoEmpresa: str
    segundoTrechoEmpresaId: int
    segundoTrechoMarca: int
    segundoTrechoOrigem: int
    segundoTrechoOrigemDescricao: str
    segundoTrechoDestino: int
    segundoTrechoDestinoDescricao: str
    segundoTrechoVende: bool
    segundoTrechoIsBpe: bool
    segundoTrechoSequencia: int
    terceiroTrechoVende: bool
    terceiroTrechoIsBpe: bool
    vende: bool
    km: float
    conexionCtrlId: int
    conexionGrupo: int
    bpe: bool
    cnpj: Optional[str] | None = None

    @property
    def primeiro_trecho_datahora_chegada(self):
        first_leg_arrival_date = self.primeiroTrechoDataChegada
        first_leg_arrivel_time = self.primeiroTrechoHoraChegada
        return datetime.strptime(
            f"{first_leg_arrival_date} {first_leg_arrivel_time}", "%d/%m/%Y %H:%M"
        )

    @property
    def segundo_trecho_datahora_partida(self):
        second_leg_departure_date = self.segundoTrechoDataSaida
        second_leg_departure_time = self.segundoTrechoHoraSaida
        return datetime.strptime(
            f"{second_leg_departure_date} {second_leg_departure_time}", "%d/%m/%Y %H:%M"
        )


@dataclass
class MapaPoltrona:
    x: str
    y: str
    disponivel: bool
    numero: str
    categoriaReservadaId: int


@dataclass
class Localidade:
    id: int
    descripcion: str
    cve: str
    indIntegracaoW2i: bool


@dataclass
class PricingSequencia:
    quantidaPoltronas: str
    precoPoltrona: str
    sequencia: str
    tipo: str


@dataclass
class PricingPoltrona:
    numero: str
    precoNumero: str
    nome: str
    pricingId: int
    porcentagem: int


@dataclass
class BuscaOnibusResponse:
    origem: Local
    destino: Local
    data: str
    servico: str
    dataSaida: str
    mapaPoltrona: list[MapaPoltrona]
    lsLocalidadeEmbarque: list[Localidade]
    lsLocalidadeDesembarque: list[Localidade]
    pricingSequencia: list[PricingSequencia]
    pricingPoltrona: list[PricingPoltrona]
    dataChegada: Optional[str] = None
    poltronasLivres: Optional[int] = None
    empresaCorridaId: Optional[int] = None
    classeServico: Optional[str] = None
    dataCorrida: Optional[str] = None


@dataclass
class Preco:
    tarifa: str
    outros: str
    pedagio: str
    seguro: str
    preco: str
    tarifaComPricing: str
    taxaEmbarque: str
    seguroW2I: str


@dataclass
class SeguroOpcional:
    km: Optional[int]
    valor: Optional[float]


@dataclass
class BloquearPoltronaResponse:
    origem: Local
    destino: Local
    data: str
    servico: str
    assento: str
    duracao: int
    transacao: str
    preco: Preco
    rutaid: str
    seguroOpcional: Optional[SeguroOpcional]
    numOperacion: str
    localizador: str
    boletoId: int
    empresaCorridaId: int
    dataSaida: str
    dataChegada: str
    dataCorrida: str
    classeServicoId: int


@dataclass
class LocalidadeParada:
    id: int
    cidade: str
    uf: str

    @property
    def cidade_com_uf(self):
        return f"{self.cidade} - {self.uf}"


@dataclass
class Parada:
    localidade: LocalidadeParada
    distancia: str  # ou float, se quiser converter (ex: float("112.96"))
    permanencia: str  # formato HH:MM
    data: str  # formato: YYYY-MM-DD
    hora: str  # formato HH:MM
    bloqueioCanalVenda: bool

    @property
    def data_hora_partida(self):
        return f"{self.data}T{self.hora}:00"


@dataclass
class ItinerarioResponse:
    servico: str
    data: str
    lsParadas: list[Parada]


class TotalbusClient:
    def __init__(
        self,
        base_url: str,
        username: str,
        password: str,
        tenant_id: str,
        timeout: int = 20,
        log_prefix: Optional[str] = None,
    ):
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            headers={
                "x-tenant-id": tenant_id,
            },
            auth=httpx.BasicAuth(username=username, password=password),
            log_prefix=f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__,
        )

    async def consultar_empresas(self):
        response = await self._client.get("/api-gateway/catalogo/consultarEmpresas")
        response.raise_for_status()
        data = response.json()
        return [dacite.from_dict(Empresa, item) for item in data]

    async def buscar_origem_destino(self, company_id: int):
        response = await self._client.get(
            f"/api-gateway/localidade/buscarOrigenDestino/{company_id}"
        )
        response.raise_for_status()
        data = response.json()
        return [dacite.from_dict(OrigemDestinos, item) for item in data]

    async def buscar_corrida(
        self, origem: int, destino: int, data: date, volta: bool = False
    ) -> SearchResponse:
        response = await self._client.post(
            "/api-gateway/consultacorrida/buscaCorrida",
            json={
                "origem": origem,
                "destino": destino,
                "data": data.isoformat(),
                "volta": volta,
            },
        )

        if (
            response.status_code == 400
            and response.json()["message"] == "Trecho não disponível"
        ):
            raise OTASectionalNotOffered

        response.raise_for_status()
        return dacite.from_dict(SearchResponse, response.json())

    async def buscar_onibus_detalhado(
        self,
        origem: int,
        destino: int,
        data: date,
        servico: int,
    ) -> BuscaOnibusResponse:
        response = await self._client.post(
            "/api-gateway/consultaonibus/buscaOnibus",
            json={
                "origem": origem,
                "destino": destino,
                "data": data.isoformat(),
                "servico": servico,
            },
        )
        response.raise_for_status()
        return dacite.from_dict(BuscaOnibusResponse, response.json())

    async def busca_itinerario_corrida(
        self, servico: int, data_corrida: str
    ) -> ItinerarioResponse:
        response = await self._client.post(
            "/api-gateway/itinerario/buscarItinerarioCorrida",
            json={"servico": servico, "data": data_corrida},
        )
        response.raise_for_status()
        return dacite.from_dict(ItinerarioResponse, response.json())

    async def bloquear_poltrona(
        self, origem: int, destino: int, data: date, servico: int, poltrona: str
    ):
        response = await self._client.post(
            "/api-gateway/bloqueiopoltrona/bloquearPoltrona",
            json={
                "origem": origem,
                "destino": destino,
                "data": data.isoformat(),
                "servico": servico,
                "poltrona": poltrona,
            },
        )
        response.raise_for_status()
        return dacite.from_dict(BloquearPoltronaResponse, response.json())

    async def desbloquear_poltrona(self, transacao: str) -> bool:
        response = await self._client.post(
            "/api-gateway/desbloqueiopoltrona/desbloquearPoltrona",
            json={"transacao": transacao},
        )
        response.raise_for_status()
        data = response.json()
        return data["status"]

    async def confirmar_venda(
        self,
        transacao: str,
        nome_passageiro: str,
        documento_passageiro: str,
        telefone: str,
    ):
        response = await self._client.post(
            "/api-gateway/confirmavenda/confirmarVenda",
            json={
                "transacao": transacao,
                "nomePassageiro": nome_passageiro,
                "documentoPassageiro": documento_passageiro,
                "telefone": telefone,
            },
        )
        response.raise_for_status()
        data = response.json()
        # TODO: type this response (create ConfirmarVendaResponse dataclass)
        return data

    async def cancelar_venda(self, transacao: str, validar_multa: bool = False):
        response = await self._client.post(
            "/api-gateway/cancelavenda/cancelarVenda",
            json={
                "transacao": transacao,
                "validarMulta": validar_multa,
            },
        )
        response.raise_for_status()
        data = response.json()
        # TODO: type this response (create ConfirmarVendaResponse dataclass)
        return data
