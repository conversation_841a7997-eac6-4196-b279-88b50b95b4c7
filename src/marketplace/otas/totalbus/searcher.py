import asyncio
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from decimal import Decimal
from typing import cast, override

import dacite
import httpx
from tenacity import (
    retry,
    retry_if_not_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    Stopover,
    Travel,
)
from marketplace.otas.exception import OTASearcherException, OTASearcherTimeoutException
from marketplace.otas.totalbus.client import (
    BloquearPoltronaResponse,
    BuscaOnibusResponse,
    ItinerarioResponse,
    OTASectionalNotOffered,
    SearchResponse,
    Servico,
    TotalbusClient,
)
from marketplace.searcher import OTASearcher, SeatUnavailable


@dataclass
class TotalbusTravelExtra:
    origem: int
    destino: int
    dataCorrida: str
    servico: int
    classe: str


class TotalbusSearcher(OTASearcher):
    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        username: str,
        password: str,
        tenant_id: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._session = TotalbusClient(
            base_url=base_url,
            username=username,
            password=password,
            tenant_id=tenant_id,
            log_prefix=self.ota_config.name,
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_not_exception_type(dacite.WrongTypeError),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
                ignored_exceptions=(OTASectionalNotOffered,),
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    @property
    def company_id(self):
        return int(self.ota_config.config["company_id"])

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        search_response, last_synced, _ = await self._search_travels(
            departure_date=departure_date,
            origem_id=origin.extra["id"],
            destino_id=destination.extra["id"],
        )
        search_response = cast(SearchResponse, search_response)

        if search_response is None:
            return []

        found = []
        for servico in search_response.lsServicos:
            if servico.vende is False:
                continue

            seat_type = self._get_ota_seat_type_by_name(servico.classe)
            company = self._get_ota_company(servico.empresaId, name=servico.empresa)
            if seat_type is None or company is None:
                continue

            travel = Travel(
                ota="totalbus",
                ota_config_id=self.ota_config.id,
                code=servico.identificadorViagem,
                service_code=servico.servico,
                company=company,
                itinerary=[],
                origin=cast(Place, origin.place),
                destination=cast(Place, destination.place),
                departure_at=datetime.fromisoformat(servico.saida),
                arrival_at=datetime.fromisoformat(servico.chegada),
                seat_type=seat_type,
                price=servico.preco,
                available_seats=servico.poltronasLivres,
                total_seats=servico.poltronasTotal,
                last_synced=last_synced,
                extra=asdict(
                    TotalbusTravelExtra(
                        origem=servico.grupoOrigemId,
                        destino=servico.grupoDestinoId,
                        dataCorrida=servico.dataCorrida,
                        servico=int(servico.servico),
                        classe=servico.classe,
                    )
                ),
            )
            if servico.conexao:
                if (
                    servico.conexao.segundo_trecho_datahora_partida
                    - servico.conexao.primeiro_trecho_datahora_chegada
                ) > timedelta(minutes=10):
                    # TODO implements connections
                    continue
                stopovers = self._build_stopovers(servico)
                if not stopovers:
                    continue
                travel.stopovers = self._build_stopovers(servico)
            found.append(travel)

        return found

    def _build_stopovers(self, servico: Servico) -> list[Stopover] | None:
        assert servico.conexao
        seat_type_second_leg = self._get_ota_seat_type_by_name(
            servico.conexao.segundoTrechoClasse
        )
        company_second_leg = self._get_ota_company(
            servico.conexao.segundoTrechoEmpresaId
        )
        if not seat_type_second_leg or not company_second_leg:
            return None
        return [
            Stopover(
                origin_ota_place_id=servico.conexao.primeiroTrechoOrigem,
                destination_ota_place_id=servico.conexao.primeiroTrechoDestino,
                extra=asdict(
                    TotalbusTravelExtra(
                        origem=servico.conexao.primeiroTrechoOrigem,
                        destino=servico.conexao.primeiroTrechoDestino,
                        dataCorrida=datetime.strptime(
                            servico.conexao.primeiroTrechoDataCorrida, "%d/%m/%Y"
                        )
                        .date()
                        .isoformat(),
                        servico=int(servico.conexao.primeiroTrechoServico),
                        classe=servico.conexao.primeiroTrechoClasse,
                    )
                ),
            ),
            Stopover(
                origin_ota_place_id=servico.conexao.segundoTrechoOrigem,
                destination_ota_place_id=servico.conexao.segundoTrechoDestino,
                extra=asdict(
                    TotalbusTravelExtra(
                        origem=servico.conexao.segundoTrechoOrigem,
                        destino=servico.conexao.segundoTrechoDestino,
                        dataCorrida=datetime.strptime(
                            servico.conexao.segundoTrechoDataCorrida, "%d/%m/%Y"
                        )
                        .date()
                        .isoformat(),
                        servico=int(servico.conexao.segundoTrechoServico),
                        classe=servico.conexao.segundoTrechoClasse,
                    )
                ),
            ),
        ]

    @override
    async def available_seats(self, travel: InputTravel) -> list[Seat]:
        response: BuscaOnibusResponse = await self._seating_map(travel)
        seats = []
        if not response.classeServico:
            # TODO: Aqui deve dar um erro
            return seats
        seat_type = self._get_ota_seat_type_by_name(response.classeServico)
        for poltrona in response.mapaPoltrona:
            seats.append(
                Seat(
                    number=poltrona.numero,
                    floor=1,
                    row=int(poltrona.x),
                    column=int(poltrona.y),
                    available=poltrona.disponivel,
                    category=response.classeServico,
                    seat_type=seat_type.seat_type if seat_type else None,
                    extra={
                        "categoriaReservadaId": poltrona.categoriaReservadaId,
                    },
                )
            )
        return seats

    @override
    async def block_seat(self, travel: Travel, seat: Seat) -> Seat:
        try:
            response = await self._session.bloquear_poltrona(
                origem=travel.extra["origem"],
                destino=travel.extra["destino"],
                data=travel.departure_at.date(),
                servico=travel.extra["servico"],
                poltrona=seat.number,
            )
            seat_type = self._get_ota_seat_type_by_name(seat.category)
            return Seat(
                number=response.assento,
                available=True,
                category=seat.category,
                seat_type=seat_type.seat_type if seat_type else None,
                price=self._seat_total_price(response),
                floor=seat.floor,
                row=seat.row,
                column=seat.column,
                extra={
                    "duracao": response.duracao,
                    **asdict(response.preco),
                },
                block_key=response.transacao,
            )
        except httpx.HTTPStatusError as error:
            if error.response.status_code == 400:
                payload = error.response.json()
                if payload["message"] == "A poltrona já foi selecionada":
                    raise SeatUnavailable
            raise

    @override
    async def unblock_seat(self, travel: Travel, seat: Seat) -> bool:
        if seat.block_key is None:
            raise ValueError("Seat does not have a `block_key` defined.")

        # TODO: handle response errors
        return await self._session.desbloquear_poltrona(transacao=seat.block_key)

    @override
    async def list_places(self) -> list[OTAPlace]:
        places = set()
        for empresa in await self.retry_policy(self._session.consultar_empresas)():
            places |= await self.list_company_places(empresa.id)
        return list(places)

    async def list_company_places(self, company_id: int) -> set[OTAPlace]:
        response = await self.retry_policy(self._session.buscar_origem_destino)(
            company_id
        )

        places = set()
        for item in response:
            places.add(
                OTAPlace(
                    ota_config_id=self.ota_config.id,
                    name=item.origem.cidade,
                    extra=item.origem,
                )
            )
            for destination in item.destinos:
                places.add(
                    OTAPlace(
                        ota_config_id=self.ota_config.id,
                        name=destination.cidade,
                        extra=destination,
                    )
                )
        return places

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        travel_extra = TotalbusTravelExtra(**travel.extra)
        itinerary: list[Checkpoint] = []
        ota_itinerary: ItinerarioResponse = await self._busca_itinerario_corrida(
            travel_extra.servico, travel_extra.dataCorrida
        )
        post_origin_checkpoint = False
        last_distance = 0
        for parada in ota_itinerary.lsParadas:
            post_origin_checkpoint = (
                post_origin_checkpoint or parada.localidade.id == travel_extra.origem
            )
            if post_origin_checkpoint:
                itinerary.append(
                    Checkpoint(
                        name=parada.localidade.cidade_com_uf,
                        departure_at=datetime.fromisoformat(parada.data_hora_partida),
                        distance_km=last_distance,
                    )
                )
            last_distance = float(parada.distancia)
            if parada.localidade.id == travel_extra.destino:
                break
        return itinerary

    @memoize(cache, ttl=3600, cache_key=lambda self: f"places:{self.ota_config.id}")
    async def _places(self) -> dict[str, OTAPlace]:
        return {place.name: place for place in await self.list_places()}

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        departure_date,
        origem_id,
        destino_id: f"search:{self.ota_config.id}:{departure_date}:{origem_id}:{destino_id}:v2",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
    )
    async def _search_travels(
        self,
        departure_date: date,
        origem_id: int,
        destino_id: int,
    ) -> tuple[SearchResponse | None, datetime | None, int]:
        try:
            search_response = await self._circuit_breaker.call(
                self._session.buscar_corrida,
                data=departure_date,
                origem=origem_id,
                destino=destino_id,
            )
            last_synced = datetime.now(timezone.utc)
            travels = search_response.lsServicos
            cache_ttl = await self._dynamic_search_ttl(
                travels, origem_id, destino_id, departure_date
            )
            return search_response, last_synced, cache_ttl
        except OTASectionalNotOffered:
            return (
                None,
                datetime.now(timezone.utc),
                self.ota_config.search_cache.ttl_not_available,
            )
        except (httpx.HTTPStatusError, CircuitOpenError) as error:
            raise OTASearcherException(
                f"Search exception for {self.__class__.__name__}"
            ) from error
        except httpx.TimeoutException as error:
            raise OTASearcherTimeoutException(
                f"Search timed out for {self.__class__.__name__}"
            ) from error

    def _has_few_seats_travel(self, search_response: list[Servico]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for servico in search_response:
            if servico.vende is True and servico.poltronasLivres < threshold:
                return True

        return False

    @memoize(
        cache,
        ttl=60,
        cache_key=lambda self, travel: f"map:{self.ota_config.id}:{travel.extra}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _seating_map(self, travel: InputTravel) -> BuscaOnibusResponse:
        return await self.retry_policy(self._session.buscar_onibus_detalhado)(
            origem=travel.extra["origem"],
            destino=travel.extra["destino"],
            data=date.fromisoformat(travel.extra["dataCorrida"]),
            servico=travel.extra["servico"],
        )

    def _seat_total_price(self, response: BloquearPoltronaResponse) -> float:
        return float(
            Decimal(response.preco.outros)
            + Decimal(response.preco.pedagio)
            + Decimal(response.preco.seguro)
            + Decimal(response.preco.preco)
            + Decimal(response.preco.taxaEmbarque)
            + Decimal(response.preco.seguroW2I)
        )

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self,
        servico,
        data_corrida: f"itinerary:{self.ota_config.id}:{servico}:{data_corrida}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _busca_itinerario_corrida(
        self, servico: int, data_corrida: str
    ) -> ItinerarioResponse:
        return await self.retry_policy(self._session.busca_itinerario_corrida)(
            servico=servico, data_corrida=data_corrida
        )
