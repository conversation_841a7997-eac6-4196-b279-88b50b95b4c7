from datetime import datetime, timedelta, timezone
from typing import cast

from marketplace.models import OTAConfig, Seat, Travel
from marketplace.otas.totalbus.client import TotalbusClient
from marketplace.tickets import Ticket, TicketInvalidStatus, TicketIssuer, TicketStatus


class TotalbusTicketIssuer(TicketIssuer):
    def __init__(self, ota_config: OTAConfig):
        super().__init__(ota_config)
        self._session = TotalbusClient(
            base_url=cast(str, ota_config.config["base_url"]),
            username=cast(str, ota_config.config["username"]),
            password=cast(str, ota_config.config["password"]),
            tenant_id=cast(str, ota_config.config["tenant_id"]),
        )

    async def issue_ticket(self, ticket: Ticket):
        if ticket.seat_block_key is None:
            raise ValueError("ticket.seat_block_key is None")

        return await self._session.confirmar_venda(
            transacao=ticket.seat_block_key,
            nome_passageiro=ticket.pax_name,
            documento_passageiro=ticket.pax_doc or "0",
            telefone=ticket.pax_phone or "0",
        )

    async def cancel_ticket(self, ticket: Ticket):
        if ticket.seat_block_key is None:
            raise ValueError("ticket.seat_block_key is None")

        if ticket.status_history[0].status != TicketStatus.ISSUED:
            raise TicketInvalidStatus("ticket status != issued")

        return await self._session.cancelar_venda(transacao=ticket.seat_block_key)

    def valid_until(
        self,
        travel: Travel,
        seat: Seat,
    ) -> datetime:
        if seat.block_key is None:
            raise ValueError("seat.block_key is None")
        return datetime.now(timezone.utc) + timedelta(
            minutes=seat.extra.get("duracao", 30)
        )
