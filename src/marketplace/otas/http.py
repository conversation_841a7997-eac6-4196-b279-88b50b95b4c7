import logging
import time

import httpx

logger = logging.getLogger(__name__)


class AsyncClient(httpx.AsyncClient):
    def __init__(self, log_prefix: str = "HTTPClient", *args, **kwargs):
        self.log_prefix = log_prefix
        super().__init__(*args, **kwargs)

    async def _send_single_request(self, request: httpx.Request) -> httpx.Response:
        start = time.monotonic()

        try:
            response = await super()._send_single_request(request)
            log_http_request_response(
                request, response, time.monotonic() - start, self.log_prefix
            )
            return response
        except Exception as exc:
            log_http_request_error(
                request, exc, time.monotonic() - start, self.log_prefix
            )
            raise


def log_http_request_response(
    request: httpx.Request,
    response: httpx.Response,
    duration: float,
    log_prefix: str = "HTTPClient",
) -> None:
    logger.info(
        "%s response %s %s (%s)",
        log_prefix,
        request.method,
        request.url,
        response.status_code,
        extra={
            "status_code": response.status_code,
            "duration": duration,
        },
    )


def log_http_request_error(
    request: httpx.Request,
    exception: Exception,
    duration: float,
    log_prefix: str = "HTTPClient",
) -> None:
    logger.warning(
        "%s response %s %s (0)",
        log_prefix,
        request.method,
        request.url,
        extra={
            "error": str(exception) or exception.__class__.__name__,
            "status_code": 0,
            "duration": duration,
        },
    )
