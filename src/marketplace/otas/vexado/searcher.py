import asyncio
import time
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from typing import cast, override

import dacite
import httpx
import jwt
from tenacity import (
    retry,
    retry_if_not_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Stopover,
    Travel,
)
from marketplace.otas.exception import OTASearcherException
from marketplace.otas.vexado.client import (
    ItinerarioResponse,
    Passagem,
    SearchResponse,
    ServiceToken,
    VexadoClient,
)
from marketplace.searcher import OTASearcher


@dataclass
class VexadoTravelExtra:
    itinerario_id: int
    rota_id: int
    empresa_id: int
    trechoOrigemId: int
    trechoDestinoId: int
    dataHoraPartida: str
    origem_id: int
    destino_id: int


class VexadoSearcher(OTASearcher):
    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        username: str,
        password: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._client = VexadoClient(base_url=base_url, log_prefix=self.ota_config.name)
        self._ota_config = ota_config
        self._token = None

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_not_exception_type(dacite.WrongTypeError),
            reraise=True,
            sleep=asyncio.sleep,
        )

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: _token_ttl(
            cast(ServiceToken, result)
        ),
        cache_key=lambda self: f"vexado:{self.ota_config.id}:token",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _get_token(self) -> ServiceToken:
        auth_response = await self._client.auth_signin(
            username=cast(str, self._ota_config.config["username"]),
            password=cast(str, self._ota_config.config["password"]),
        )
        return auth_response.service_token

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        token = await self._get_token()
        search_response, last_synced, _ = await self._search_travels(
            service_token=token,
            origem_id=origin.extra["id"],
            destino_id=destination.extra["id"],
            departure_date=departure_date,
        )
        if search_response is None:
            return []

        search_response = cast(SearchResponse, search_response)

        travels = []
        for passagem in search_response.passagensIda:
            seat_type = self._get_ota_seat_type_by_name(passagem.tipoVeiculo)
            company = self._get_ota_company(
                passagem.idEmpresa, name=passagem.nomeEmpresa
            )

            if seat_type is None or company is None:
                continue

            travel = Travel(
                ota="vexado",
                ota_config_id=self.ota_config.id,
                code=f"{passagem.idEmpresa}:{passagem.idRota}:{passagem.idItinerario}",
                service_code=str(passagem.idItinerario),
                company=company,
                itinerary=[],
                origin=cast(Place, origin.place),
                destination=cast(Place, destination.place),
                departure_at=datetime.fromisoformat(passagem.dataHoraPartida),
                arrival_at=datetime.fromisoformat(passagem.dataHoraChegada),
                seat_type=seat_type,
                price=float(passagem.preco),
                available_seats=passagem.assentosDisponiveis,
                total_seats=passagem.assentosDisponiveis,
                last_synced=last_synced,
                extra=asdict(
                    VexadoTravelExtra(
                        itinerario_id=passagem.idItinerario,
                        rota_id=passagem.idRota,
                        empresa_id=passagem.idEmpresa,
                        trechoOrigemId=passagem.trechoOrigemId,
                        trechoDestinoId=passagem.trechoDestinoId,
                        origem_id=origin.extra["id"],
                        destino_id=destination.extra["id"],
                        dataHoraPartida=passagem.dataHoraPartida,
                    )
                ),
            )
            if passagem.cidadesComConexao:
                # TODO The OTA needs to return the stopover locations correctly
                travel.stopovers = [
                    Stopover(
                        origin_ota_place_id=origin.extra["id"],
                        destination_ota_place_id=passagem.cidadesComConexao[
                            0
                        ].idCidadeOrigem,
                    )
                ] + [
                    Stopover(
                        origin_ota_place_id=cidade.idCidadeOrigem,
                        destination_ota_place_id=cidade.idCidadeDestino,
                    )
                    for cidade in passagem.cidadesComConexao
                ]
                travel.single_ticket_connection = True

            travels.append(travel)

        return travels

    @override
    async def list_places(self) -> list[OTAPlace]:
        token = await self._get_token()

        if self.ota_config.config.get("company_id"):
            external_ids = [int(self.ota_config.config["company_id"])]
        else:
            companies_with_cnpj = [
                c for c in (self.ota_config.companies or []) if c.cnpj
            ]
            external_ids = [company.external_id for company in companies_with_cnpj]

        if not external_ids:
            return []

        tasks = []
        for external_id in external_ids:
            tasks.append(
                self.retry_policy(self._client.list_cities_by_company)(
                    service_token=token, company_id=external_id
                )
            )

        responses = await asyncio.gather(*tasks)
        cities = [city for cities_response in responses for city in cities_response]
        places = set()
        for city in cities:
            places.add(
                OTAPlace(
                    ota_config_id=self.ota_config.id,
                    name=f"{city.nome} - {city.uf}",
                    extra=city,
                )
            )

        return list(places)

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        travel_extra = VexadoTravelExtra(**travel.extra)
        itinerary: list[Checkpoint] = []
        token = await self._get_token()
        ota_itinerary: ItinerarioResponse = await self._itinerary(
            token, travel_extra.empresa_id, travel_extra.itinerario_id
        )
        post_origin_checkpoint = False
        data_hora_partida = datetime.fromisoformat(travel_extra.dataHoraPartida)
        for trecho in ota_itinerary.rotaDto.trechosDto:
            post_origin_checkpoint = (
                post_origin_checkpoint
                or trecho.cidadeDestino.id == travel_extra.origem_id
            )
            if not post_origin_checkpoint:
                continue

            origin_checkpoint = trecho.cidadeDestino.id == travel_extra.origem_id
            data_hora_partida = (
                data_hora_partida
                if origin_checkpoint
                else data_hora_partida + timedelta(seconds=trecho.duracao_segundos)
            )
            itinerary.append(
                Checkpoint(
                    name=trecho.cidadeDestino.nomeComUf,
                    departure_at=data_hora_partida,
                    distance_km=0
                    if origin_checkpoint
                    else float(trecho.quilometragem.replace(",", ".")),
                )
            )
            if trecho.cidadeDestino.id == travel_extra.destino_id:
                break
        return itinerary

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        service_token,
        origem_id,
        destino_id,
        departure_date: f"search:{self.ota_config.id}:{departure_date}:{origem_id}:{destino_id}:v2",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
        process_result=lambda result, self, *a, **kw: self._trace_process_result(
            result
        ),  # pyright: ignore
    )
    async def _search_travels(
        self,
        service_token: ServiceToken,
        origem_id: int,
        destino_id: int,
        departure_date: date,
    ) -> tuple[SearchResponse | None, datetime | None, int]:
        try:
            result = await self._client.search(
                service_token,
                origem_id=origem_id,
                destino_id=destino_id,
                data_ida=departure_date,
            )
            last_synced = datetime.now(timezone.utc)
            cache_ttl = await self._dynamic_search_ttl(
                result.passagensIda, origem_id, destino_id, departure_date
            )
            return result, last_synced, cache_ttl
        except httpx.HTTPStatusError as error:
            raise OTASearcherException(
                f"Search exception for {self.__class__.__name__}"
            ) from error
        except httpx.TimeoutException as error:
            # TODO: This implementation currently updates the cache with `None` values when a timeout occurs.
            #       A more effective approach would be to implement a circuit breaker pattern. This would allow
            #       the TimeoutException to propagate without updating the cache. When the circuit is open, the
            #       cache would remain unchanged, preventing stale data from being stored.
            return None, None, self._dynamic_search_ttl_on_error(error)
            # raise OTASearcherTimeoutException(f"Search timed out for {searcher.__class__.__name__}") from error

    def _has_few_seats_travel(self, search_response: list[Passagem]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for servico in search_response:
            if servico.assentosDisponiveis < threshold:
                return True

        return False

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self,
        service_token,
        company_id,
        itinerario_id: f"itinerary:{self.ota_config.id}:{company_id}:{itinerario_id}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _itinerary(
        self, service_token: ServiceToken, company_id: int, itinerario_id: int
    ) -> ItinerarioResponse:
        return await self.retry_policy(self._client.itinerary)(
            service_token=service_token,
            company_id=company_id,
            itinerario_id=itinerario_id,
        )


def _token_ttl(token: ServiceToken) -> int:
    payload = jwt.decode(token.token, options={"verify_signature": False})
    return payload["exp"] - int(time.time()) - 120
