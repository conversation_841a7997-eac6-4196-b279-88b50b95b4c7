from dataclasses import asdict, dataclass
from datetime import date
from typing import Optional

import dacite

from marketplace.otas.http import AsyncClient


@dataclass
class TravelSummary:
    id: int
    line_code: str
    departure_date: str
    departure_time: str
    description: str
    company_id: int
    schedule_id: int


@dataclass
class Sectional:
    code: str
    description: str
    id: int
    uf_acronym: str


@dataclass
class TravelLeg:
    arrival_zone: str
    departure_time_zone: str
    local_arrival_date_time: str
    local_exit: str
    seccional_code: str
    seccional_id: int
    seccional_name: str
    stop_time: str
    total_time: str
    total_km: float
    uf_acronym: str


@dataclass
class TravelDetail:
    origin_id: int
    origin_name: str
    destination_id: int
    destination_name: str
    class_code: str
    seats: int
    capacity: int
    class_description: str


@dataclass
class ClassCategory:
    seat_map_id: int
    vehicle_type_id: int
    description: str
    short_description: str
    category_sat: str
    initial_seat: int
    final_seat: int
    free_seats: int


@dataclass
class Tariff:
    tariff: float
    insurance: float
    toll: float
    boarding_fee: float
    ferry: float
    additional: float
    calculation: float
    referential: float
    resource_discount: float
    total_km: float
    price_promotional: float
    amount: float
    category: ClassCategory


@dataclass
class Company:
    id: int
    code: str
    name: str
    logo: str


@dataclass
class ClassInfo:
    id: int
    long_name: str
    short_name: str


@dataclass
class Item:
    id: int
    road_item_id: int
    gateway_id: int
    gateway_type: str
    station_id_origin: int
    station_id_destiny: int
    service_travel: str
    reference_travel: str
    datetime_departure: str
    datetime_arrival: str
    duration: str
    free_seats: int
    tariff: float
    insurance: float
    fee: float
    travel_item_toll: float
    price: float
    price_promotional: float
    tax: float
    class_info: ClassInfo
    company: Company
    line_code: str
    direction: str
    vehicle_id: int
    tariffs: Optional[list[Tariff]]


@dataclass
class FreeSeatBenefit:
    type: str
    free: int
    amount: int


@dataclass
class Travel:
    key: str
    id: int
    origin_sectional_id: int
    destiny_sectional_id: int
    datetime_departure: str
    datetime_arrival: str
    duration: str
    price: float
    price_promotional: float
    free_seats: int
    free_seats_benefits: list[FreeSeatBenefit] | None
    items: Optional[list[Item]]


@dataclass
class BenefitValue:
    type: str
    tarrif: float
    price_discount: float
    amount: float


@dataclass
class Seat:
    number: int
    line: int
    column: int
    busy: bool
    category: str
    tariff: float
    amount: float
    aditional_amount: float
    price_discount: float
    woman_space: bool
    benefits_values: list[BenefitValue]


@dataclass
class SeatingMap:
    floor_1: list[Seat]
    floor_2: Optional[list[Seat]]
    free_seats: int

    @property
    def seats(self):
        if self.floor_2:
            return self.floor_1 + self.floor_2
        return self.floor_1


@dataclass
class BlockSeatRequest:
    seat: int
    type: str = "internet"
    required_compation: bool = False


@dataclass
class BlockSeatResponse:
    selected_seat_key: str


class EulabsClient:
    def __init__(
        self,
        base_url: str,
        api_id: str,
        api_key: str,
        timeout: int = 20,
        log_prefix: Optional[str] = None,
    ):
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            headers={
                "X-Eucatur-Api-Id": api_id,
                "X-Eucatur-Api-Key": api_key,
            },
            log_prefix=f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__,
        )

    async def travels_search(
        self, departure_date: date, origin_sectional_id: int, destiny_sectional_id: int
    ) -> list[Travel]:
        response = await self._client.get(
            "/road/travels/search",
            params={
                "departure_date": departure_date.isoformat(),
                "origin_sectional_id": origin_sectional_id,
                "destiny_sectional_id": destiny_sectional_id,
            },
        )
        response.raise_for_status()

        payload = response.json()

        for travel in payload:
            for item in travel.get("items") or []:
                item["class_info"] = item.pop("class")

        return [dacite.from_dict(Travel, item) for item in payload]

    async def sectionals(
        self,
        id: Optional[int] = None,
        code: Optional[str] = None,
        is_road_station: bool = False,
        locality_id: Optional[int] = None,
    ):
        response = await self._client.get(
            "/sectionals",
            params={
                "id": id,
                "code": code,
                "is_road_station": is_road_station,
                "locality_id": locality_id,
            },
        )
        response.raise_for_status()
        payload = response.json()
        return [dacite.from_dict(Sectional, item) for item in payload]

    async def seating_map(self, travel_key: str):
        response = await self._client.get(f"/road/travels/{travel_key}/seating-map")
        response.raise_for_status()
        payload = response.json()
        return [dacite.from_dict(SeatingMap, item) for item in payload]

    async def block_seats(self, travel_key: str, block_seats: list[BlockSeatRequest]):
        response = await self._client.post(
            f"/road/travels/{travel_key}/seats",
            json={"items": [asdict(block_seat) for block_seat in block_seats]},
        )
        response.raise_for_status()
        return BlockSeatResponse(selected_seat_key=response.json()["selected_seat_Key"])

    async def unblock_seats(self, travel_key: str, seat_key: str):
        response = await self._client.delete(
            f"/road/travels/{travel_key}/selected_seats/{seat_key}"
        )
        response.raise_for_status()

    async def list_road_travels_summary(
        self, initial_departure_date: date, final_departure_date: date
    ) -> list[TravelSummary]:
        response = await self._client.get(
            "/road/travels/summary",
            params={
                "initial_departure_date": initial_departure_date.isoformat(),
                "final_departure_date": final_departure_date.isoformat(),
            },
        )
        response.raise_for_status()

        payload = response.json()
        return [TravelSummary(**item) for item in payload]

    async def get_road_travel_summary(self, travel_id: int) -> list[TravelLeg]:
        response = await self._client.get(f"/road/travels/summary/{travel_id}")
        response.raise_for_status()

        payload = response.json()
        return [TravelLeg(**item) for item in payload]

    async def get_road_travel_detail(self, travel_id: int) -> list[TravelDetail]:
        response = await self._client.get(f"/road/travels/detail/{travel_id}")
        response.raise_for_status()

        payload = response.json()
        return [TravelDetail(class_code=item.pop("class"), **item) for item in payload]
