from dataclasses import dataclass
from datetime import date
from typing import Any

from marketplace.otas.http import AsyncClient


@dataclass
class AuthResponse:
    accessToken: str
    refreshToken: str
    accessTokenSso: str
    refreshTokenSso: str
    expiresAt: str
    expiresSec: int
    currentDate: str

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            accessToken=raw["accessToken"],
            refreshToken=raw["refreshToken"],
            accessTokenSso=raw["accessTokenSso"],
            refreshTokenSso=raw["refreshTokenSso"],
            expiresAt=raw["expiresAt"],
            expiresSec=raw["expiresSec"],
            currentDate=raw["currentDate"],
        )


@dataclass
class Location:
    id: int
    name: str
    city: str
    state: str
    ibgeCityCode: str

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            name=raw["name"],
            city=raw["city"],
            state=raw["state"],
            ibgeCityCode=raw["ibgeCityCode"],
        )


@dataclass
class Journey:
    origin: str
    destination: str
    departure: str
    arrival: str
    service: str
    busCompany: str
    busType: str
    operationType: str
    amenities: str | None
    distance: float | None
    stopover: bool
    freeSeats: int
    price: float
    kind: str | None
    message: str
    hasIntinerary: bool
    queryOnly: bool
    connection: bool
    noSeatNumberRequired: bool
    busId: str | float | None
    discounts: list[Any]
    companyDiscount: float
    minAdvanceTime: str | float | None
    minAdvanceTimeInMinutes: str | float | None
    valueToAdd: str | float | None
    expirationDate: str | float | None
    inTransit: bool | None

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            origin=raw["origin"],
            destination=raw["destination"],
            departure=raw["departure"],
            arrival=raw["arrival"],
            service=raw["service"],
            busCompany=raw["busCompany"],
            busType=raw["busType"],
            operationType=raw["operationType"],
            amenities=raw.get("amenities"),
            distance=raw.get("distance"),
            stopover=raw["stopover"],
            freeSeats=raw["freeSeats"],
            price=raw["price"],
            kind=raw["kind"],
            message=raw["message"],
            hasIntinerary=raw["hasIntinerary"],
            queryOnly=raw["queryOnly"],
            connection=raw["connection"],
            noSeatNumberRequired=raw["noSeatNumberRequired"],
            busId=raw.get("busId"),
            discounts=raw["discounts"],
            companyDiscount=raw["companyDiscount"],
            minAdvanceTime=raw.get("minAdvanceTime"),
            minAdvanceTimeInMinutes=raw.get("minAdvanceTimeInMinutes"),
            valueToAdd=raw.get("valueToAdd"),
            expirationDate=raw.get("expirationDate"),
            inTransit=raw.get("inTransit"),
        )


@dataclass
class Checkpoint:
    id: int
    name: str
    sequence: int
    travelTime: str

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            name=raw["name"],
            sequence=raw["sequence"],
            travelTime=raw["travelTime"],
        )


@dataclass
class TicketType:
    id: int
    description: str
    salesStrategy: str
    priceValue: float
    isNominalSale: bool
    allowsPromotion: bool
    code: str
    validateRule: bool | None
    numberSeat: int | None
    amount: float | None
    quotType: str | None

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            description=raw["description"],
            salesStrategy=raw["salesStrategy"],
            priceValue=raw["priceValue"],
            isNominalSale=raw["isNominalSale"],
            allowsPromotion=raw["allowsPromotion"],
            code=raw["code"],
            validateRule=raw.get("validateRule"),
            numberSeat=raw.get("numberSeat"),
            amount=raw.get("amount"),
            quotType=raw.get("quotType"),
        )


@dataclass
class Seat:
    status: str
    x: int
    y: int
    z: int
    number: str
    description: str
    ticketType: list[TicketType]

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            status=raw["status"],
            x=raw["x"],
            y=raw["y"],
            z=raw["z"],
            number=raw["number"],
            description=raw["description"],
            ticketType=[TicketType.from_dict(tt) for tt in raw["ticketType"]],
        )

    @property
    def default_price(self) -> float | None:
        for ticket_type in self.ticketType:
            if ticket_type.salesStrategy == "DEFAULT":
                return ticket_type.priceValue


@dataclass
class PriceInfo:
    basePrice: float
    insurancePrice: float
    taxPrice: float
    otherPrice: float
    tollPrice: float
    boardingPrice: float
    commission: float
    companyDiscount: float
    discounts: list[Any]
    cancelationFee: float | None
    price: float
    totalDiscount: float
    priceWithoutInsurance: float
    totalCompanyDiscount: float
    originalPriceWithoutInsurance: float
    priceWithBusCompanyDiscount: float
    priceWithInsurance: float
    originalPrice: float

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            basePrice=raw["basePrice"],
            insurancePrice=raw["insurancePrice"],
            taxPrice=raw["taxPrice"],
            otherPrice=raw["otherPrice"],
            tollPrice=raw["tollPrice"],
            boardingPrice=raw["boardingPrice"],
            commission=raw["commission"],
            companyDiscount=raw["companyDiscount"],
            discounts=raw["discounts"],
            cancelationFee=raw.get("cancelationFee"),
            price=raw["price"],
            totalDiscount=raw["totalDiscount"],
            priceWithoutInsurance=raw["priceWithoutInsurance"],
            totalCompanyDiscount=raw["totalCompanyDiscount"],
            originalPriceWithoutInsurance=raw["originalPriceWithoutInsurance"],
            priceWithBusCompanyDiscount=raw["priceWithBusCompanyDiscount"],
            priceWithInsurance=raw["priceWithInsurance"],
            originalPrice=raw["originalPrice"],
        )


@dataclass
class Quota:
    id: int
    description: str
    salesStrategy: str
    priceValue: float
    isNominalSale: bool
    allowsPromotion: bool
    code: str
    validateRule: bool | None
    numberSeat: int | None
    amount: float | None
    quotType: str | None

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            id=raw["id"],
            description=raw["description"],
            salesStrategy=raw["salesStrategy"],
            priceValue=raw["priceValue"],
            isNominalSale=raw["isNominalSale"],
            allowsPromotion=raw["allowsPromotion"],
            code=raw["code"],
            validateRule=raw.get("validateRule"),
            numberSeat=raw.get("numberSeat"),
            amount=raw.get("amount"),
            quotType=raw.get("quotType"),
        )


@dataclass
class JourneyInfo:
    seats: list[Seat]
    travel: Journey
    priceInfo: PriceInfo
    listQuotas: list[Quota]

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(
            seats=[Seat.from_dict(s) for s in raw["seats"]],
            travel=Journey.from_dict(raw["travel"]),
            priceInfo=PriceInfo.from_dict(raw["priceInfo"]),
            listQuotas=[Quota.from_dict(q) for q in raw["listQuotas"]],
        )


@dataclass
class BusType:
    name: str
    id: int

    @classmethod
    def from_dict(cls, raw: dict):
        return cls(name=raw["name"], id=raw["id"])


class GuichepassClient:
    def __init__(
        self,
        base_url: str,
        client_id: str = "WEB_SALE",
        timeout: int = 20,
        log_prefix: str | None = None,
    ):
        self._client = AsyncClient(
            base_url=base_url,
            timeout=timeout,
            headers={"ClientID": client_id},
            log_prefix=f"{log_prefix} {self.__class__.__name__}"
            if log_prefix
            else self.__class__.__name__,
        )

    async def auth_login(self, username: str, password: str) -> AuthResponse:
        response = await self._client.post(
            "/auth/login",
            headers={
                "username": username,
                "password": password,
            },
        )
        response.raise_for_status()

        return AuthResponse.from_dict(response.json())

    async def locations(self, auth: AuthResponse) -> list[Location]:
        response = await self._client.get(
            "/web-sale/leg-stops/no-page", headers={"X-Authorization": auth.accessToken}
        )

        response.raise_for_status()
        return [Location.from_dict(t) for t in response.json()]

    async def journeys_search(
        self, auth: AuthResponse, origin: int, destination: int, departure_date: date
    ) -> list[Journey]:
        response = await self._client.post(
            "/web-sale/search",
            headers={"X-Authorization": auth.accessToken},
            json={
                "origin": origin,
                "destination": destination,
                "date": departure_date.isoformat(),
            },
        )

        response.raise_for_status()
        return [Journey.from_dict(t) for t in response.json()]

    async def get_itinerary(self, auth: AuthResponse, service: str) -> list[Checkpoint]:
        response = await self._client.get(
            f"/web-sale/service/{service}/itinerary",
            headers={"X-Authorization": auth.accessToken},
        )

        response.raise_for_status()
        return [Checkpoint.from_dict(t) for t in response.json()]

    async def get_bus_layout(
        self,
        auth: AuthResponse,
        departure_date: date,
        origin: int,
        destination: int,
        company_id: str,
        service: str,
    ) -> JourneyInfo:
        response = await self._client.post(
            "/web-sale/bus-layout",
            headers={"X-Authorization": auth.accessToken},
            json={
                "date": departure_date.isoformat(),
                "origin": origin,
                "destination": destination,
                "busCompany": company_id,
                "service": service,
            },
        )

        response.raise_for_status()
        return JourneyInfo.from_dict(response.json())

    async def get_bus_types(self, auth: AuthResponse) -> list[BusType]:
        response = await self._client.get(
            "/web-sale/seatTypes", headers={"X-Authorization": auth.accessToken}
        )

        response.raise_for_status()
        return [BusType.from_dict(bt) for bt in response.json()]
