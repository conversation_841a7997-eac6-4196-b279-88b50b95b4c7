import asyncio
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta, timezone
from typing import cast, override

import dacite
import httpx
from tenacity import (
    retry,
    retry_if_not_exception_type,
    stop_after_attempt,
    wait_random_exponential,
)
from yapcache import memoize

from marketplace.caching import cache, lock
from marketplace.circuit_breaker import (
    CircuitBreaker,
    CircuitBreakerConfig,
    CircuitOpenError,
    NullCircuitBreaker,
)
from marketplace.models import (
    Checkpoint,
    InputTravel,
    OTAConfig,
    OTAPlace,
    Place,
    Seat,
    Travel,
)
from marketplace.otas.exception import OTASearcherException, OTASearcherTimeoutException
from marketplace.otas.guichepass.client import (
    AuthResponse,
    GuichepassClient,
    Journey,
    JourneyInfo,
)
from marketplace.otas.guichepass.client import (
    Checkpoint as GuichepassCheckpoint,
)
from marketplace.searcher import OTASearcher


@dataclass
class GuichepassTravelExtra:
    origin: str
    destination: str
    departure: str
    busCompany: str
    service: str


class GuichepassSearcher(OTASearcher):
    def __init__(
        self,
        ota_config: OTAConfig,
        base_url: str,
        client_id: str,
        username: str,
        password: str,
        **kwargs,
    ):
        super().__init__(ota_config)
        self._session = GuichepassClient(
            base_url=base_url,
            client_id=client_id,
            log_prefix=self.ota_config.name,
        )

        self.retry_policy = retry(
            wait=wait_random_exponential(multiplier=1.5, min=0.5, max=5),
            stop=stop_after_attempt(3),
            retry=retry_if_not_exception_type(dacite.WrongTypeError),
            reraise=True,
            sleep=asyncio.sleep,
        )

        if self.ota_config.circuit_breaker.enabled:
            self._circuit_breaker = CircuitBreaker(
                config=CircuitBreakerConfig(
                    failure_threshold=self.ota_config.circuit_breaker.failure_threshold,
                    failure_window=timedelta(
                        seconds=self.ota_config.circuit_breaker.failure_window
                    ),
                    recovery_timeout=timedelta(
                        seconds=self.ota_config.circuit_breaker.recovery_timeout
                    ),
                    recovery_test_requests=self.ota_config.circuit_breaker.recovery_test_requests,
                ),
                name=f"{self.__class__.__name__} {self.ota_config.name}",
            )
        else:
            self._circuit_breaker = NullCircuitBreaker()

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: cast(AuthResponse, result).expiresSec,
        cache_key=lambda self: f"guichepass:{self.ota_config.id}:token",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _get_auth(self) -> AuthResponse:
        auth_response = await self._session.auth_login(
            username=cast(str, self.ota_config.config["username"]),
            password=cast(str, self.ota_config.config["password"]),
        )
        return auth_response

    @override
    async def search(
        self,
        origin: OTAPlace,
        destination: OTAPlace,
        departure_date: date,
    ) -> list[Travel]:
        auth = await self._get_auth()
        travels, last_synced, _ = await self._search_travels(
            auth=auth,
            departure_date=departure_date,
            origin=origin.extra["id"],
            destination=destination.extra["id"],
        )
        journeys = cast(list[Journey], travels)

        found = []
        for journey in journeys:
            company = self._get_ota_company(int(journey.busCompany))
            bus_type = await self._get_bus_type(journey.busType)
            seat_type = self._get_ota_seat_type_by_name(bus_type)
            if company is None or seat_type is None:
                continue

            found.append(
                Travel(
                    ota="guichepass",
                    ota_config_id=self.ota_config.id,
                    code=f"{journey.service}_{journey.origin}_{journey.destination}_{journey.service}_{journey.busCompany}",
                    service_code=str(journey.service),
                    company=company,
                    itinerary=[],
                    origin=cast(Place, origin.place),
                    destination=cast(Place, destination.place),
                    departure_at=datetime.fromisoformat(journey.departure),
                    arrival_at=datetime.fromisoformat(journey.arrival),
                    seat_type=seat_type,
                    price=journey.price,
                    available_seats=journey.freeSeats,
                    total_seats=journey.freeSeats,
                    last_synced=last_synced,
                    extra=asdict(
                        GuichepassTravelExtra(
                            origin=journey.origin,
                            destination=journey.destination,
                            departure=journey.departure,
                            busCompany=journey.busCompany,
                            service=journey.service,
                        )
                    ),
                )
            )

        return found

    def _has_few_seats_travel(self, search_response: list[Journey]) -> bool:
        threshold = self.ota_config.search_cache.few_seats_threshold
        for travel in search_response:
            if travel.freeSeats < threshold:
                return True

        return False

    async def _get_bus_type(self, bus_type_id: str):
        bus_types_map = await self._bus_types_map()
        return bus_types_map[int(bus_type_id)]

    @memoize(cache, ttl=86400, cache_key=lambda self: f"bustypes_:{self.ota_config.id}")
    async def _bus_types_map(self) -> dict[int, str]:
        auth = await self._get_auth()
        bus_types = await self._session.get_bus_types(auth=auth)
        return {bt.id: bt.name for bt in bus_types}

    @override
    async def list_places(self) -> list[OTAPlace]:
        auth = await self._get_auth()

        locations = await self.retry_policy(self._session.locations)(auth=auth)

        places = set()
        for location in locations:
            places.add(
                OTAPlace(
                    ota_config_id=self.ota_config.id,
                    name=f"{location.name} - {location.state}",
                    extra=location,
                )
            )

        return list(places)

    @override
    async def available_seats(self, travel: InputTravel) -> list[Seat]:
        auth = await self._get_auth()
        seats = []
        journey_info = await self._seating_map(
            auth=auth,
            departure_date=datetime.fromisoformat(travel.extra["departure"]).date(),
            origin=travel.extra["origin"],
            destination=travel.extra["destination"],
            company_id=travel.extra["busCompany"],
            service=travel.extra["service"],
        )
        journey_info = cast(JourneyInfo, journey_info)
        for seat in journey_info.seats:
            seat_type = self._get_ota_seat_type_by_name(seat.description)
            if not seat_type:
                continue
            seats.append(
                Seat(
                    number=seat.number,
                    floor=seat.z + 1,
                    row=seat.x + 1,
                    column=seat.y + 1,
                    available=seat.status == "FREE",
                    category=seat.description,
                    seat_type=seat_type.seat_type if seat_type else None,
                    price=seat.default_price,
                    extra={"description": seat.description},
                )
            )

        return seats

    async def travel_itinerary(self, travel: InputTravel) -> list[Checkpoint]:
        def time_str_to_timedelta(time_str: str) -> timedelta:
            hours, minutes, seconds = time_str.split(":")
            return timedelta(
                hours=int(hours), minutes=int(minutes), seconds=int(seconds)
            )

        travel_extra = GuichepassTravelExtra(**travel.extra)
        itinerary: list[Checkpoint] = []
        post_origin_checkpoint = False
        full_itinerary = await self._travel_itinerary(
            await self._get_auth(), travel_extra.service
        )
        initial_departure = datetime.fromisoformat(travel_extra.departure)
        itinerary: list[Checkpoint] = []
        before_init_duration = timedelta(minutes=0)
        for cp in cast(list[GuichepassCheckpoint], full_itinerary):
            if cp.id == int(travel_extra.origin):
                post_origin_checkpoint = True
                before_init_duration = time_str_to_timedelta(cp.travelTime)
            if post_origin_checkpoint:
                duration = time_str_to_timedelta(cp.travelTime) - before_init_duration
                itinerary.append(
                    Checkpoint(
                        name=cp.name,
                        departure_at=initial_departure + duration,
                        distance_km=duration.total_seconds() * 60 / 3600,
                    )
                )
                if cp.id == int(travel_extra.destination):
                    break
        return itinerary

    @memoize(
        cache,
        ttl=lambda result, self, *args, **kwargs: result[2],  # pyright: ignore
        cache_key=lambda self,
        auth,
        departure_date,
        origin,
        destination: f"search:{self.ota_config.id}:{departure_date}:{origin}:{destination}:v3",
        lock=lock,
        best_before=lambda result, self, *args, **kwargs: self._dynamic_best_before(
            result[2]  # pyright: ignore
        ),
        process_result=lambda result, self, *a, **kw: self._trace_process_result(
            result
        ),  # pyright: ignore
    )
    async def _search_travels(
        self,
        auth: AuthResponse,
        departure_date: date,
        origin: int,
        destination: int,
    ) -> tuple[list[Journey] | None, datetime | None, int]:
        try:
            travels = await self._circuit_breaker.call(
                self.retry_policy(self._session.journeys_search),
                auth=auth,
                departure_date=departure_date,
                origin=origin,
                destination=destination,
            )
            last_synced = datetime.now(timezone.utc)
            cache_ttl = await self._dynamic_search_ttl(
                travels,
                origin,
                destination,
                departure_date,
            )
            return travels, last_synced, cache_ttl
        except (httpx.HTTPStatusError, CircuitOpenError) as error:
            raise OTASearcherException(
                f"Search exception for {self.__class__.__name__}"
            ) from error
        except httpx.TimeoutException as error:
            raise OTASearcherTimeoutException(
                f"Search timed out for {self.__class__.__name__}"
            ) from error

    @memoize(
        cache,
        ttl=86400,
        cache_key=lambda self,
        auth,
        service: f"itinerary:{self.ota_config.id}:{service}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _travel_itinerary(
        self, auth: AuthResponse, service: str
    ) -> list[GuichepassCheckpoint]:
        return await self.retry_policy(self._session.get_itinerary)(
            auth=auth, service=service
        )

    @memoize(
        cache,
        ttl=60,
        cache_key=lambda self,
        auth,
        departure_date,
        origin,
        destination,
        company_id,
        service: f"map:{self.ota_config.id}:{departure_date.isoformat()}:{origin}:{destination}:{company_id}:{service}",
        best_before=lambda self, *args, **kwargs: float("inf"),
    )
    async def _seating_map(
        self,
        auth: AuthResponse,
        departure_date: date,
        origin: int,
        destination: int,
        company_id: str,
        service: str,
    ) -> JourneyInfo:
        return await self.retry_policy(self._session.get_bus_layout)(
            auth=auth,
            departure_date=departure_date,
            origin=origin,
            destination=destination,
            company_id=company_id,
            service=service,
        )
