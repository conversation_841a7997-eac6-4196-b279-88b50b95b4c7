from dataclasses import dataclass

import asyncpg
from starlette.requests import Request

from marketplace import (
    get_ota_config_by_id,
    ticket_issuer,
    tickets,
)
from marketplace.models import (
    Pax,
    Seat,
    Travel,
    from_dict,
)
from marketplace.views import OrjsonResponse


@dataclass
class TicketRequest:
    client_code: str
    pax: Pax
    seat: Seat
    travel: Travel
    tags: dict[str, str]


async def list_tickets(request: Request):
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        items = await tickets.list_tickets(db_conn)
        return OrjsonResponse(items)


async def create_ticket(request: Request):
    payload = await request.json()
    ticket_request = from_dict(TicketRequest, payload)

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_config = await get_ota_config_by_id(
            db_conn, ticket_request.travel.ota_config_id
        )

        async with db_conn.transaction():
            try:
                issuer = ticket_issuer(ota_config)
                new_ticket = issuer.create_ticket(
                    client_code=ticket_request.client_code,
                    travel=ticket_request.travel,
                    seat=ticket_request.seat,
                    pax=ticket_request.pax,
                    tags=ticket_request.tags,
                )

                ticket_id = await tickets.insert_ticket(db_conn, new_ticket)
                return OrjsonResponse({"id": ticket_id}, status_code=201)
            except tickets.TicketAlreadyExists as exc:
                return OrjsonResponse({"message": str(exc)}, status_code=409)


async def confirm_ticket(request: Request):
    ticket_id = request.path_params["ticket_id"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ticket = await tickets.get_ticket_by_id(db_conn, ticket_id)

        try:
            ota_config = await get_ota_config_by_id(db_conn, ticket.ota_config.id)
            issuer = ticket_issuer(ota_config)
            # TODO: put ticket into the processing queue (should I create a queued status?)
            await tickets.insert_ticket_status(
                db_conn, ticket.id, tickets.TicketStatus.PROCESSING
            )
            result = await issuer.issue_ticket(ticket)
            await tickets.insert_ticket_status(
                db_conn,
                ticket.id,
                tickets.TicketStatus.ISSUED,
                result=result,
            )
            return OrjsonResponse({"id": ticket_id}, status_code=200)
        except Exception:
            await tickets.insert_ticket_status(
                db_conn, ticket.id, tickets.TicketStatus.FAILED
            )
            raise


async def cancel_ticket(request: Request):
    ticket_id = request.path_params["ticket_id"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ticket = await tickets.get_ticket_by_id(db_conn, ticket_id)

        ota_config = await get_ota_config_by_id(db_conn, ticket.ota_config.id)
        issuer = ticket_issuer(ota_config)
        await tickets.insert_ticket_status(
            db_conn, ticket.id, tickets.TicketStatus.CANCELING
        )
        result = await issuer.cancel_ticket(ticket)
        await tickets.insert_ticket_status(
            db_conn,
            ticket.id,
            tickets.TicketStatus.CANCELED,
            result=result,
        )

        return OrjsonResponse({"id": ticket_id}, status_code=200)
