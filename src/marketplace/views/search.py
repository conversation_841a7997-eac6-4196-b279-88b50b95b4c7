import asyncio
import itertools
import logging
from datetime import date, datetime, timedelta, timezone

import asyncpg
from opentelemetry import trace
from starlette.requests import Request

from marketplace import (
    database,
    get_searcher_from_ota_config,
    places,
)
from marketplace import list_ota_configs as list_otas
from marketplace.models import OTAConfig, OTAPlace
from marketplace.views import OrjsonResponse, bad_request_response

logger = logging.getLogger()


async def search_places(request: Request):
    q = request.query_params.get("q")
    if q is None:
        return bad_request_response

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        result = await database.queries.search_places(db_conn, q=q)
        return OrjsonResponse({"items": [dict(record) for record in result]})


async def search_travels(request: Request):
    origin = request.query_params.get("origin")
    destination = request.query_params.get("destination")
    start_date = request.query_params.get("start_date")
    end_date = request.query_params.get("end_date")
    if not (origin and destination and start_date and end_date):
        return bad_request_response

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        result = await database.queries.search_travels(
            db_conn,
            origin=origin,
            destination=destination,
            start_date=date.fromisoformat(start_date),
            end_date=date.fromisoformat(end_date),
        )
        return OrjsonResponse({"items": [dict(record) for record in result]})


async def search_travels_hot(request: Request):
    origin = request.query_params.get("origin")
    destination = request.query_params.get("destination")
    departure_date = request.query_params.get("date")
    timeout = float(request.query_params.get("timeout", 5))

    if not (origin and destination and departure_date):
        return bad_request_response

    span = trace.get_current_span()
    span.set_attributes(
        {
            "origin": origin,
            "destination": destination,
            "departure_date": departure_date,
        }
    )

    departure_date = date.fromisoformat(departure_date)
    response = await _search_travels(
        request, origin, destination, departure_date, timeout
    )
    travels = list(
        itertools.chain.from_iterable([r.travels for r in response if r.travels])
    )
    span.set_attribute("count", len(travels))
    return OrjsonResponse({"items": travels})


async def search_travels_hot_v3(request: Request):
    origin = request.query_params.get("origin")
    destination = request.query_params.get("destination")
    departure_date = request.query_params.get("date")
    timeout = float(request.query_params.get("timeout", 5))

    if not (origin and destination and departure_date) or origin == destination:
        return bad_request_response

    span = trace.get_current_span()
    span.set_attributes(
        {
            "origin": origin,
            "destination": destination,
            "departure_date": departure_date,
        }
    )

    departure_date = date.fromisoformat(departure_date)
    if departure_date < (datetime.now(timezone.utc) - timedelta(hours=4)).date():
        return bad_request_response

    responses = await _search_travels(
        request, origin, destination, departure_date, timeout
    )
    count_travels = sum(len(i.travels) for i in responses if i.travels)
    span.set_attribute("count", count_travels)
    return OrjsonResponse({"items": responses})


async def _search_travels(request, origin, destination, departure_date, timeout):
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_configs = await list_otas(db_conn)
        ota_places = await places.list_ota_places_by_slugs(
            db_conn, [origin, destination]
        )

    search_tasks = []
    for ota_config in ota_configs:
        ota_config_places = ota_places.get(ota_config.id)
        if ota_config_places is None:
            continue

        origins, destinations = [], []
        for k, v in ota_config_places.items():
            if k and k.startswith(origin):
                origins += v
            if k and k.startswith(destination):
                destinations += v

        if not origins or not destinations:
            continue

        task = _search_travels_ota(
            ota_config=ota_config,
            origins=origins,
            destinations=destinations,
            departure_date=departure_date,
            timeout=timeout,
        )
        search_tasks.append(task)

    if not search_tasks:  # no OTA has this origin and destination as places
        return []

    response = await asyncio.gather(*search_tasks)

    return list(itertools.chain.from_iterable([r for r in response]))


tracer = trace.get_tracer(__name__)


async def _search_travels_ota(
    ota_config: OTAConfig,
    origins: list[OTAPlace],
    destinations: list[OTAPlace],
    departure_date: date,
    timeout: float,
):
    logger.debug("search %s (%s)", ota_config.name, departure_date)
    searcher = get_searcher_from_ota_config(ota_config)
    search_result = await searcher.search_multiple(
        origins=origins,
        destinations=destinations,
        departure_date=departure_date,
        timeout=timeout,
    )
    return search_result
