from dataclasses import asdict
from http import HTTPStatus

import asyncpg
from starlette.requests import Request

from marketplace import (
    get_searcher_from_travel,
)
from marketplace.models import (
    InputTravel,
    Seat,
    Travel,
    from_dict,
)
from marketplace.searcher import SeatUnavailable
from marketplace.views import OrjsonResponse


async def travel_seating_map(request: Request):
    travel = from_dict(InputTravel, await request.json())

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_travel(db_conn, travel)
        seats = await searcher.available_seats(travel=travel)
        return OrjsonResponse({"seats": [asdict(seat) for seat in seats]})


async def travel_block_seat(request: Request):
    payload = await request.json()

    travel = from_dict(Travel, payload["travel"])
    seat = from_dict(Seat, payload["seat"])

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_travel(db_conn, travel)
        try:
            seat = await searcher.block_seat(travel=travel, seat=seat)
            return OrjsonResponse(asdict(seat))
        except SeatUnavailable:
            return OrjsonResponse(
                {"code": "SEAT_UNAVAILABLE"},
                status_code=HTTPStatus.CONFLICT,
            )


async def travel_unblock_seat(request: Request):
    payload = await request.json()

    travel = from_dict(Travel, payload["travel"])
    seat = from_dict(Seat, payload["seat"])

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_travel(db_conn, travel)
        ok = await searcher.unblock_seat(travel=travel, seat=seat)
        return OrjsonResponse({"ok": ok})
