from dataclasses import asdict

import asyncpg
from starlette.requests import Request

from marketplace import (
    database,
    places,
)
from marketplace.views import OrjsonResponse, bad_request_response


async def list_places(request: Request):
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        all_places = await places.list_all_places(db_conn)
        return OrjsonResponse({"items": [asdict(place) for place in all_places]})


async def save_place(request: Request):
    data = await request.json()

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        if "id" in data:
            record = await database.queries.update_place(db_conn, **data)
        else:
            record = await database.queries.insert_place(db_conn, **data)
        return OrjsonResponse(dict(record))


async def update_place_status(request: Request):
    place_id = request.path_params["place_id"]
    data = await request.json()

    if "status" not in data:
        return bad_request_response

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.update_place_status(
            db_conn,
            id=int(place_id),
            status=data["status"],
        )
        return OrjsonResponse(dict(record))
