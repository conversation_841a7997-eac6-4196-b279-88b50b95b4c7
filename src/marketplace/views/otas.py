from dataclasses import asdict

import asyncpg
from starlette.requests import Request

from marketplace import (
    companies,
    database,
    get_ota_config_by_id,
    places,
)
from marketplace import list_ota_configs as list_otas
from marketplace.views import OrjsonResponse, bad_request_response


async def refresh_places_from_ota(request: Request):
    config_id = int(request.path_params["config_id"])
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_config = await get_ota_config_by_id(db_conn, config_id)
        await places.refresh_ota_places(db_conn, ota_config)
        return OrjsonResponse({"ok": True})


async def list_ota_places(request: Request):
    config_id = int(request.path_params["config_id"])
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_places = await places.list_ota_places(db_conn, config_id=config_id)
        return OrjsonResponse({"items": [asdict(place) for place in ota_places]})


async def save_ota_company(request: Request):
    data = await request.json()
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.update_ota_company(db_conn, **data)
        return OrjsonResponse(dict(record))


async def list_ota_configs(request: Request):
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_configs = await list_otas(db_conn, status=None)
        return OrjsonResponse([asdict(ota_config) for ota_config in ota_configs])


async def list_ota_configs_companies(request: Request):
    status = request.query_params.get("ota_status")

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        all_companies = await companies.list_all_ota_companies(db_conn, status=status)
        return OrjsonResponse({"items": [asdict(company) for company in all_companies]})


async def get_ota_config(request: Request):
    config_id = int(request.path_params["config_id"])

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        ota_config = await get_ota_config_by_id(db_conn, config_id=config_id)
        return OrjsonResponse(asdict(ota_config))


async def save_ota_config(request: Request):
    data = await request.json()

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        if "id" in data:
            record = await database.queries.update_ota_config(db_conn, **data)
        else:
            record = await database.queries.insert_ota_config(db_conn, **data)
        return OrjsonResponse(dict(record))


async def update_ota_config_status(request: Request):
    config_id = request.path_params["config_id"]
    data = await request.json()

    if "status" not in data:
        return bad_request_response

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.update_ota_config_status(
            db_conn,
            id=int(config_id),
            status=data["status"],
        )
        return OrjsonResponse(dict(record))


async def save_ota_place(request: Request):
    config_id = int(request.path_params["config_id"])
    data = await request.json()

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.update_ota_config_place(
            db_conn,
            ota_config_id=config_id,
            name=data["name"],
            status=data["status"],
            place=places.slug_to_ltree(data["place"]["slug"]),
        )
        return OrjsonResponse(dict(record))


async def save_ota_seat_type(request: Request):
    config_id = int(request.path_params["config_id"])
    data = await request.json()

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.update_ota_seat_type(
            db_conn,
            ota_config_id=config_id,
            name=data["name"],
            status=data["status"],
            seat_type=data["seat_type"],
        )
        return OrjsonResponse(dict(record))
