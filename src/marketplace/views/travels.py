from dataclasses import asdict

import asyncpg
from starlette.requests import Request

from marketplace import (
    database,
    get_searcher_from_travel,
)
from marketplace.models import (
    InputTravel,
    Travel,
    from_dict,
)
from marketplace.views import OrjsonResponse, bad_request_response


async def travel_price(request: Request):
    travel_code = request.path_params["travel_code"]

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        record = await database.queries.get_travel(db_conn, travel_code=travel_code)
        travel = from_dict(Travel, record)

        searcher = await get_searcher_from_travel(db_conn, travel)
        price = await searcher.travel_price(travel=travel)
        return OrjsonResponse({"price": price})


async def travel_itinerary(request: Request):
    travel = from_dict(InputTravel, await request.json())

    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        searcher = await get_searcher_from_travel(db_conn, travel)

    try:
        checkpoints = await searcher.travel_itinerary(travel=travel)
    except (<PERSON><PERSON><PERSON><PERSON>, KeyError):
        return bad_request_response
    return OrjsonResponse({"itinerary": [asdict(cp) for cp in checkpoints]})
