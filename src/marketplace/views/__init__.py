from decimal import Decimal
from typing import Any

import orjson
from starlette.responses import JSONResponse

bad_request_response = JSONResponse({"message": "Bad request."}, 400)


class OrjsonResponse(JSONResponse):
    def render(self, content: Any) -> bytes:
        return orjson.dumps(content, default=self._default)

    def _default(self, obj):
        if isinstance(obj, Decimal):
            return str(obj)
        raise TypeError
