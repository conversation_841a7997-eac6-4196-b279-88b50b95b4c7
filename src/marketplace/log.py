import logging
import random

from marketplace.correlation_context.context import correlation_id_var


class SampleRateFilter(logging.Filter):
    def __init__(self, rate=1.0):
        self.rate = rate

    def filter(self, record: logging.LogRecord):
        return random.random() < self.rate


class CorrelationIdFilter(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        record.correlation_id = correlation_id_var.get()
        return True
