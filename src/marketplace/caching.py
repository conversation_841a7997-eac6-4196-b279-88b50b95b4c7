import asyncio

from opentelemetry import trace
from redis.asyncio import Redis
from yapcache.caches import MultiLayerCache
from yapcache.caches.memory import InMemoryCache
from yapcache.caches.null import NullCache
from yapcache.caches.redis import RedisCache
from yapcache.distlock import NullLock, RedisDistLock

from marketplace.settings import (
    REDIS_DB,
    REDIS_HOST,
    REDIS_MAX_CONNECTIONS,
    REDIS_PORT,
    TESTING,
)

tracer = trace.get_tracer(__name__)


class TracedRedisDistLock(RedisDistLock):
    async def _acquire(self):
        new_event = asyncio.Event()
        while True:
            event = RedisDistLock._EVENTS.setdefault(self.resource_name, new_event)
            # If an event already exists for this resource, another coroutine is attempting to acquire or
            # have acquired the lock, so wait for that coroutine to finish
            if event != new_event:
                with tracer.start_as_current_span("wait_previous_event"):
                    await event.wait()

            acquired = await self.client.set(
                self.resource_name, self.lock_id, nx=True, px=self.timeout * 1000
            )
            if acquired:
                return self

            # another coroutine in the same thread finished or `delay_interval` passed.
            try:
                await asyncio.wait_for(event.wait(), timeout=self.delay_interval)
            except asyncio.TimeoutError:
                ...


redis_client = Redis(
    host=REDIS_HOST,
    port=REDIS_PORT,
    db=REDIS_DB,
    max_connections=REDIS_MAX_CONNECTIONS,
)


if not TESTING:
    cache = MultiLayerCache([InMemoryCache(maxsize=2_000), RedisCache(redis_client)])

    lock = lambda key: TracedRedisDistLock(redis_client, key, timeout=60)  # noqa:E731
else:
    cache = NullCache()
    lock = lambda key: NullLock()  # noqa:E731
