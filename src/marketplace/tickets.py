import logging
from abc import ABC, abstractmethod
from dataclasses import asdict, dataclass, field
from datetime import date, datetime, timedelta, timezone
from decimal import Decimal
from enum import StrEnum, auto
from typing import Any

import asyncpg

from marketplace import database
from marketplace.models import (
    OTACompany,
    OTAConfig,
    Pax,
    Place,
    Seat,
    Travel,
    from_dict,
)

logger = logging.getLogger(__name__)


class TicketStatus(StrEnum):
    CREATED = auto()
    EXPIRED = auto()
    PROCESSING = auto()
    ISSUED = auto()
    FAILED = auto()
    CANCELING = auto()
    CANCELED = auto()


@dataclass
class _BaseTicket:
    client_code: str
    tags: dict[str, str]
    valid_until: datetime

    # pax
    pax_name: str
    pax_doc: str | None
    pax_phone: str | None
    pax_birthdate: date

    # travel
    code: str
    departure_at: datetime
    arrival_at: datetime
    seat_type: str
    travel_extra: Any

    # seat
    seat_number: str
    seat_floor: int
    seat_row: int
    seat_column: int
    seat_extra: Any
    seat_block_key: str | None
    price: Decimal


@dataclass
class Ticket(_BaseTicket):
    id: int
    created_at: datetime
    ota_config: OTAConfig
    company: OTACompany
    origin: Place
    destination: Place
    expired: bool
    status_history: list["TicketStatusHistory"] = field(default_factory=lambda: [])


@dataclass
class NewTicket(_BaseTicket):
    ota_config_id: int
    company_id: int
    origin: str
    destination: str


@dataclass
class TicketStatusHistory:
    status: TicketStatus
    created_at: datetime


class TicketIssuer(ABC):
    def __init__(self, ota_config: OTAConfig):
        self.ota_config = ota_config

    def create_ticket(
        self,
        client_code: str,
        travel: Travel,
        seat: Seat,
        pax: Pax,
        tags: dict[str, str],
    ) -> NewTicket:
        if seat.price is None:
            raise ValueError("seat.price is None")

        if travel.company is None:
            raise ValueError("travel.company is None")

        return NewTicket(
            client_code=client_code,
            tags=tags,
            valid_until=self.valid_until(travel=travel, seat=seat),
            # pax
            pax_name=pax.name,
            pax_doc=pax.doc,
            pax_phone=pax.phone,
            pax_birthdate=pax.birthdate,
            # travel
            ota_config_id=travel.ota_config_id,
            code=travel.code,
            company_id=travel.company.external_id,
            origin=travel.origin.slug,
            destination=travel.destination.slug,
            departure_at=travel.departure_at,
            arrival_at=travel.arrival_at,
            seat_type=travel.seat_type.name,
            travel_extra=travel.extra,
            # seat
            seat_number=seat.number,
            seat_floor=seat.floor,
            seat_row=seat.row,
            seat_column=seat.column,
            seat_extra=seat.extra,
            seat_block_key=seat.block_key,
            price=Decimal.from_float(seat.price),
        )

    def valid_until(
        self,
        travel: Travel,
        seat: Seat,
    ) -> datetime:
        interval = timedelta(minutes=15)
        logger.warning("Using default valid_until interval; interval=%s", interval)
        return datetime.now(timezone.utc) + interval

    @abstractmethod
    async def issue_ticket(self, ticket: Ticket): ...

    @abstractmethod
    async def cancel_ticket(self, ticket: Ticket): ...


async def insert_ticket(db_conn: asyncpg.Connection, ticket: NewTicket) -> int:
    from marketplace.places import slug_to_ltree

    try:
        row = asdict(ticket)
        row["origin"] = slug_to_ltree(row["origin"])
        row["destination"] = slug_to_ltree(row["destination"])

        ticket_id = await database.queries.insert_ticket(db_conn, **row)
        await insert_ticket_status(db_conn, ticket_id, TicketStatus.CREATED)
        return ticket_id
    except asyncpg.UniqueViolationError:
        raise TicketAlreadyExists(f'Ticket "{ticket.client_code}" already exists.')


async def insert_ticket_status(
    db_conn,
    ticket_id: int,
    status: TicketStatus,
    result: Any | None = None,
):
    await database.queries.insert_ticket_status(
        db_conn,
        ticket_id=ticket_id,
        status=status,
        result=result,
    )


async def list_tickets(db_conn: asyncpg.Connection):
    rows = await database.queries.list_tickets(db_conn)
    return [from_dict(Ticket, row) for row in rows]


async def get_ticket_by_id(db_conn: asyncpg.Connection, ticket_id: int):
    row = await database.queries.get_ticket_by_id(db_conn, ticket_id=ticket_id)
    return from_dict(Ticket, row)


class TicketAlreadyExists(RuntimeError): ...


class TicketInvalidStatus(RuntimeError): ...
