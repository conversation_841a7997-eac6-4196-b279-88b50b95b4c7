import contextlib
import logging
from typing import AsyncIterator, TypedDict

import asyncpg
from opentelemetry.instrumentation.starlette import StarletteInstrumentor
from starlette.applications import Starlette
from starlette.requests import Request
from starlette.routing import Route

from marketplace import (
    database,
)
from marketplace.background_tasks import launch_background_task
from marketplace.caching import cache, redis_client
from marketplace.correlation_context.middleware import CorrelationIdMiddleware
from marketplace.settings import DATABASE_URL
from marketplace.views import (
    OrjsonResponse,
    otas,
    places,
    search,
    seats,
    tickets,
    travels,
)
from marketplace.worker import WORKER

logger = logging.getLogger()


async def health(request: Request):
    db_pool: asyncpg.Pool = request.state.db_pool
    async with db_pool.acquire() as db_conn:
        return OrjsonResponse(
            {
                "ok": True,
                "services": {
                    "db": await db_conn.fetchval("SELECT true"),
                    "cache": await cache.set("health", True, ttl=1) is None,
                },
            }
        )


class State(TypedDict):
    db_pool: asyncpg.Pool


@contextlib.asynccontextmanager
async def _lifespan(app: Starlette) -> AsyncIterator[State]:
    await launch_background_task(WORKER.run())

    async with database.create_connection_pool(DATABASE_URL) as db_pool:
        yield {
            "db_pool": db_pool,
        }

    # Close the Redis connection when the application is shutting down
    await redis_client.aclose()


app = Starlette(
    debug=True,
    routes=[
        Route("/health", health),
        Route("/search/places", search.search_places),
        Route("/search/travels", search.search_travels),
        Route("/search/travels/hot", search.search_travels_hot),
        Route("/search/travels/hot/v3", search.search_travels_hot_v3),
        Route("/travels/{travel_code}/price", travels.travel_price),
        Route("/travels/itinerary", travels.travel_itinerary, methods=["POST"]),
        Route("/travels/seating-map", seats.travel_seating_map, methods=["POST"]),
        Route("/travels/block-seat", seats.travel_block_seat, methods=["POST"]),
        Route("/travels/unblock-seat", seats.travel_unblock_seat, methods=["POST"]),
        Route("/tickets", tickets.list_tickets, methods=["GET"]),
        Route("/tickets", tickets.create_ticket, methods=["POST"]),
        Route(
            "/tickets/{ticket_id:int}/confirm",
            tickets.confirm_ticket,
            methods=["POST"],
        ),
        Route(
            "/tickets/{ticket_id:int}/cancel",
            tickets.cancel_ticket,
            methods=["POST"],
        ),
        Route("/otas", otas.list_ota_configs),
        Route("/otas", otas.save_ota_config, methods=["POST"]),
        Route("/otas/companies", otas.list_ota_configs_companies),
        Route("/otas/{config_id}", otas.get_ota_config),
        Route(
            "/otas/{config_id}/status",
            otas.update_ota_config_status,
            methods=["POST"],
        ),
        Route("/otas/{config_id}/places", otas.list_ota_places),
        Route("/otas/{config_id}/places/refresh", otas.refresh_places_from_ota),
        Route("/otas/{config_id}/places", otas.save_ota_place, methods=["POST"]),
        Route(
            "/otas/{config_id}/seat-types",
            otas.save_ota_seat_type,
            methods=["POST"],
        ),
        Route("/otas/{config_id}/companies", otas.save_ota_company, methods=["POST"]),
        Route("/places", places.list_places),
        Route("/places", places.save_place, methods=["POST"]),
        Route(
            "/places/{place_id}/status",
            places.update_place_status,
            methods=["POST"],
        ),
    ],
    lifespan=_lifespan,
)
StarletteInstrumentor.instrument_app(app)
app.add_middleware(CorrelationIdMiddleware)
