-- name: list_tickets
select
  tickets.id,
  tickets.client_code,
  tickets.tags,
  tickets.created_at,
  tickets.valid_until,
  tickets.valid_until < current_timestamp as expired,
  tickets.pax_name,
  tickets.pax_doc,
  tickets.pax_phone,
  tickets.pax_birthdate,
  tickets.code,
  tickets.company_id,
  tickets.departure_at,
  tickets.arrival_at,
  tickets.seat_type,
  tickets.travel_extra,
  tickets.seat_number,
  tickets.seat_floor,
  tickets.seat_row,
  tickets.seat_column,
  tickets.seat_extra,
  tickets.seat_block_key,
  tickets.price,
  to_jsonb(ota_config_companies) as company,
  to_jsonb(ota_configs) as ota_config,
  to_jsonb(places_origin) as origin,
  to_jsonb(places_destination) as destination,
  (select array_agg(to_jsonb(v)) from (
      select ticket_status_history as v
      from ticket_status_history
      where tickets.id = ticket_status_history.ticket_id
      order by ticket_status_history.created_at desc
  ) as s) as status_history
from tickets
join ota_configs on tickets.ota_config_id = ota_configs.id
join ota_config_companies on tickets.ota_config_id = ota_config_companies.ota_config_id and tickets.company_id = ota_config_companies.external_id
join places places_origin on tickets.origin = places_origin.slug
join places places_destination on tickets.destination = places_destination.slug
order by tickets.created_at desc;

-- name: get_ticket_by_id^
select
  tickets.id,
  tickets.client_code,
  tickets.tags,
  tickets.created_at,
  tickets.valid_until,
  tickets.valid_until < current_timestamp as expired,
  tickets.pax_name,
  tickets.pax_doc,
  tickets.pax_phone,
  tickets.pax_birthdate,
  tickets.code,
  tickets.company_id,
  tickets.departure_at,
  tickets.arrival_at,
  tickets.seat_type,
  tickets.travel_extra,
  tickets.seat_number,
  tickets.seat_floor,
  tickets.seat_row,
  tickets.seat_column,
  tickets.seat_extra,
  tickets.seat_block_key,
  tickets.price,
  to_jsonb(ota_config_companies) as company,
  to_jsonb(ota_configs) as ota_config,
  to_jsonb(places_origin) as origin,
  to_jsonb(places_destination) as destination,
  (select array_agg(to_jsonb(v)) from (
      select ticket_status_history as v
      from ticket_status_history
      where tickets.id = ticket_status_history.ticket_id
      order by ticket_status_history.created_at desc
  ) as s) as status_history
from tickets
join ota_configs on tickets.ota_config_id = ota_configs.id
join ota_config_companies on tickets.ota_config_id = ota_config_companies.ota_config_id and tickets.company_id = ota_config_companies.external_id
join places places_origin on tickets.origin = places_origin.slug
join places places_destination on tickets.destination = places_destination.slug
where tickets.id = :ticket_id;


-- name: insert_ticket<!
insert into tickets (
    client_code,
    tags,
    valid_until,
    pax_name,
    pax_doc,
    pax_phone,
    pax_birthdate,
    ota_config_id,
    code,
    company_id,
    origin,
    destination,
    departure_at,
    arrival_at,
    seat_type,
    travel_extra,
    seat_number,
    seat_floor,
    seat_row,
    seat_column,
    seat_extra,
    seat_block_key,
    price
) values (
    :client_code,
    :tags,
    :valid_until,
    :pax_name,
    :pax_doc,
    :pax_phone,
    :pax_birthdate,
    :ota_config_id,
    :code,
    :company_id,
    :origin,
    :destination,
    :departure_at,
    :arrival_at,
    :seat_type,
    :travel_extra,
    :seat_number,
    :seat_floor,
    :seat_row,
    :seat_column,
    :seat_extra,
    :seat_block_key,
    :price
) returning id;

-- name: insert_ticket_status<!
insert into ticket_status_history (
    ticket_id,
    created_at,
    status,
    result
) values (
    :ticket_id,
    current_timestamp,
    :status,
    :result
) returning *;
