-- name: search_places
select id, name, replace(slug::text, '_', '-') as slug
from places
order by slug::varchar <-> :q asc  -- trigam similarity
limit 10;

-- name: list_places
select * from places order by name;

-- name: list_places_with_subplaces
select
  subpath(places.slug, 0, 1) as slug,
  array_agg(subplaces.slug) filter (where subplaces.slug != places.slug) as subplaces
from places
join places subplaces on subplaces.slug <@ places.slug
where nlevel(places.slug) = 1
group by 1;


-- name: list_ota_places_by_slugs
select
    op.ota_config_id,
    op.name,
    jsonb_build_object(
        'name', p.name,
        'slug', replace(p.slug::text, '_', '-'),
        'status', p.status,
        'created_at', p.created_at
    ) as place,
    op.status,
    op.created_at,
    op.extra
from ota_config_places op
join ota_configs c on op.ota_config_id = c.id
join places p on op.place = p.slug
where op.place <@ any(:slugs) and c.status = 'active'
order by op.ota_config_id asc

-- name: insert_places*!
insert into places (
    name,
    slug,
    status
) values (
    :name,
    :slug,
    :status
)
on conflict (slug) do update set
  name = excluded.name,
  status = excluded.status,
  updated_at = current_timestamp;

-- name: insert_place<!
insert into places (name, slug)
values (:name, :slug)
returning *;

-- name: update_place<!
update places
set
  name = :name,
  slug = :slug,
  status = :status
where id = :id
returning *;

-- name: update_place_status<!
update places
set
  status = :status,
  updated_at = current_timestamp
where id = :id
returning id, status;

-- name: list_ota_config_places
select
    op.ota_config_id,
    op.name,
    op.extra,
    op.status,
    op.created_at,
    to_jsonb(p.*) as place
from ota_config_places op
left join places p on p.slug = op.place
where op.ota_config_id = :config_id
order by op.name;

-- name: insert_ota_config_places*!
insert into ota_config_places (
    ota_config_id,
    name,
    extra,
    status,
    place
) values (
    :ota_config_id,
    :name,
    :extra,
    :status,
    :place
)
on conflict (ota_config_id, name) do update set
    extra = excluded.extra,
    status = excluded.status,
    place = excluded.place
returning *;

-- name: update_ota_config_place<!
update ota_config_places
set
    status = :status,
    place = replace(:place, '-', '_')::ltree,
    updated_at = current_timestamp
where ota_config_id = :ota_config_id and name = :name
returning *;
