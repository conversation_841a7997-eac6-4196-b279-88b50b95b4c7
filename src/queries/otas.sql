-- name: list_ota_configs
select
  c.*,
  array_agg(distinct t.*) filter (where t.name is not null) as seat_types,
  array_agg(distinct cp.*) filter (where cp.external_id is not null) as companies
from ota_configs c
left join ota_config_seat_types t on t.ota_config_id = c.id
left join ota_config_companies cp on cp.ota_config_id = c.id
group by c.id;

-- name: get_ota_config_from_id^
select
  c.*,
  array_agg(distinct t.*) filter (where t.name is not null) as seat_types,
  array_agg(distinct cp.*) filter (where cp.external_id is not null) as companies
from ota_configs c
left join ota_config_seat_types t on t.ota_config_id = c.id
left join ota_config_companies cp on cp.ota_config_id = c.id
where id = :id
group by c.id

-- name: get_ota_config_by_name^
select * from ota_configs where name = :name;

-- name: insert_ota_config<!
insert into ota_configs (name, provider, config, search_cache, circuit_breaker)
values (:name, :provider, :config, :search_cache, :circuit_breaker)
returning *;

-- name: update_ota_config<!
update ota_configs
set
  name = :name,
  provider = :provider,
  config = :config,
  search_cache = :search_cache,
  circuit_breaker = :circuit_breaker,
  updated_at = current_timestamp,
  status = :status
where id = :id
returning *;

-- name: update_ota_config_status<!
update ota_configs
set
  status = :status,
  updated_at = current_timestamp
where id = :id
returning id, status;
