---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: marketplace
  namespace: arq
  labels:
    app: marketplace
spec:
  replicas: 3
  selector:
    matchLabels:
      app: marketplace
  template:
    metadata:
      labels:
        app: marketplace
    spec:
      initContainers:
        - name: db-migration
          image: $CI_IMAGE
          command: ["python", "-m", "marketplace", "db", "up"]
          envFrom:
            - secretRef:
                name: marketplace
      containers:
        - name: marketplace
          image: $CI_IMAGE
          imagePullPolicy: Always
          ports:
            - containerPort: 8000
          envFrom:
            - secretRef:
                name: marketplace
          resources:
            requests:
              memory: "500Mi"
              cpu: "300m"
            limits:
              memory: "800Mi"
              cpu: "550m"
          readinessProbe:
            httpGet:
              path: /health
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: marketplace
  namespace: arq
  labels:
    app: marketplace
spec:
  type: ClusterIP
  ports:
    - port: 8000
      targetPort: 8000
      protocol: TCP
  selector:
    app: marketplace

---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: marketplace
  namespace: arq
  labels:
    app: marketplace
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx-internal
  rules:
    - host: marketplace.buserdev.com.br
      http:
        paths:
          - path: /api(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: marketplace
                port:
                  number: 8000
    - host: marketplace.buser.vpc
      http:
        paths:
          - path: /api(/|$)(.*)
            pathType: ImplementationSpecific
            backend:
              service:
                name: marketplace
                port:
                  number: 8000

---
apiVersion: dragonflydb.io/v1alpha1
kind: Dragonfly
metadata:
  namespace: arq
  name: marketplace-dragonfly
  labels:
    app.kubernetes.io/name: marketplace-dragonfly
    app.kubernetes.io/instance: marketplace-dragonfly
    app.kubernetes.io/part-of: marketplace
spec:
  replicas: 2
  args: ["--dbfilename=marketplace-dragonfly-snapshot"]
  resources:
    requests:
      cpu: 50m
      memory: 512Mi
    limits:
      cpu: 500m
      memory: 512Mi
  nodeSelector:
    instanceType: general
  topologySpreadConstraints:
    - maxSkew: 1
      topologyKey: topology.kubernetes.io/zone
      whenUnsatisfiable: ScheduleAnyway
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "9999"
  snapshot:
    cron: "*/15 * * * *"
    persistentVolumeClaimSpec:
      accessModes:
        - ReadWriteOnce
      resources:
        requests:
          storage: 8Gi
